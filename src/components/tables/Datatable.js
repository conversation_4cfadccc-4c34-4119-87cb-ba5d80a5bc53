import React, { Component } from 'react';
import PropTypes from 'prop-types';
import $ from 'jquery';
import _ from 'lodash';
import { objectWithoutProperties } from '../../components/utilities/Helpers';

require('datatables.net-bs');
require('datatables.net-bs4/js/dataTables.bootstrap4.js');
require('datatables.net-bs4/css/dataTables.bootstrap4.css');
require('datatables.net-responsive');
require('datatables.net-responsive-bs');
require('datatables.net-responsive-bs/css/responsive.bootstrap.css');


let isEmpty = value => value == null || value === '';

let getCellValue = ({prop, defaultContent, render}, row) =>
    !isEmpty(prop) && isEmpty(row[prop]) ? defaultContent : render ? render(row[prop], row) : row[prop];

let getCellClass = ({prop, className}, row) =>
    !isEmpty(prop) && isEmpty(row[prop]) ? 'empty-cell' : typeof className === 'function' ? className(row[prop], row) : className;

class Datatable extends Component {

    static propTypes = {

        columns: PropTypes.arrayOf(PropTypes.shape({

            title: PropTypes.string.isRequired,

            prop: PropTypes.oneOfType([
                PropTypes.string,
                PropTypes.number
            ]),

            render: PropTypes.func,

            sortable: PropTypes.bool,

            defaultContent: PropTypes.string,

            width: PropTypes.oneOfType([
                PropTypes.string,
                PropTypes.number
            ]),

            className: PropTypes.oneOfType([
                PropTypes.string,
                PropTypes.func
            ]),

            enableSearch: PropTypes.bool,

            filter: PropTypes.array

        })).isRequired,

        searching: PropTypes.bool,

        paging: PropTypes.bool,

        ordering: PropTypes.bool,

        info: PropTypes.bool,

        dataArray: PropTypes.arrayOf(PropTypes.oneOfType([
            PropTypes.array,
            PropTypes.object
        ])).isRequired,

        buildRowOptions: PropTypes.func,

        sortBy: PropTypes.shape({
            column: PropTypes.number,
            mode: PropTypes.oneOf(['asc', 'desc'])
        }),

        order: PropTypes.array,

        withBoard: PropTypes.bool,

        withBordered: PropTypes.bool
    };


    static defaultProps = {
        paging: false,
        searching: false,
        ordering: false,
        info: false,
        buildRowOptions: () => ({}),
        sortBy: {},
        withBordered: false
    };

    constructor(props) {
        super(props);
        this._headers = [];
        this.postRender = this.postRender.bind(this);
    }

    shouldComponentUpdate(nextProps) {
        const isEqualDataArray = _.isEqual(this.props.dataArray, nextProps.dataArray);
        // 销毁上一个table
        !isEqualDataArray && $(this.element).dataTable().api().destroy();
        return !isEqualDataArray;
    }

    componentDidUpdate() {

        this.postRender();

    }

    componentDidMount() {

        this.postRender()

    }

    componentWillUnmount() {

        $(this.element).dataTable({destroy: true});

    }

    postRender() {

        let {columns, paging, searching, ordering, order, info, withBoard, retrieve, withBordered} = this.props;

        let domTemplate = withBoard ? `<"card-body table-responsive p-0 ${withBordered ? 'table-bordered' : ''}" <"tools" fi>t><"card-footer" lp>` :
            `fi<"table-responsive ${withBordered ? 'table-bordered' : ''}" t>lp`;

        let options = {
            dom: domTemplate,
            paging: paging,
            searching: searching,
            ordering: ordering,
            order: order,
            info: info,
            retrieve: retrieve,
            responsive: false,
            oLanguage: {
                sSearch: '搜索',
                sLengthMenu: '每页展示 _MENU_ 条记录',
                info: '第 _PAGE_ 页 (共 _PAGES_ 页)',
                zeroRecords: 'Nothing found - sorry',
                infoEmpty: '无数据',
                infoFiltered: '(filtered from _MAX_ total records)',
                oPaginate: {
                    sNext: '<i class="fa fa-caret-right"></i>',
                    sPrevious: '<i class="fa fa-caret-left"></i>'
                },
                sEmptyTable: "暂无数据"
            },
            initComplete: function() {
                this.api().columns().every( function() {
                    let column = this;

                    let settings  = columns[column.index()];

                    if(settings.enableSearch && searching) {

                        if(settings.filter && settings.filter.length) {
                            let select = $('<select class="form-control form-control-sm"><option value=""></option></select>')
                                .appendTo( $(column.header()) )
                                .on( 'change', function () {
                                    var val = $(this).val();
                                    column.search( val ? '^'+val+'$' : '', true, false ).draw();
                                } );

                            $.each(settings.filter,  function ( k, v ) {
                                select.append( '<option value="'+v+'">'+v+'</option>' )
                            } )
                        }else{
                            $('<input class="form-control form-control-sm" style="height: 33px" type="text"/>')
                                .appendTo( $(column.header()) )
                                .on( 'keyup change', function () {
                                    if ( column.search() !== $(this).val() ) {
                                        column.search( $(this).val() ).draw();
                                    }
                                } );
                        }
                    }

                    return '';

                } );
            },
        };

        $(this.element).dataTable(options);

    }

    setRef = node => this.element = node;

    render() {


        let {columns, buildRowOptions, dataArray } = this.props;

        let headers = columns.map(
            (col, idx) =>
                <th
                    ref={c => this._headers[idx] = c}
                    key={idx}
                    style={{width: col.width}}
                    className={col.className}
                    role="columnheader"
                    scope="col">
                    <span>{col.title}</span>
                </th>
        );

        let rows = dataArray.map((row, i) =>
            <tr key={i} {...buildRowOptions(row)}>
                {
                    columns.map((col, i) =>
                        <td key={i} className={getCellClass(col, row)}>
                            {getCellValue(col, row)}
                        </td>
                    )
                }
            </tr>
        );
        // 这里我也不想这么写,但是新数据回来之后,当列表是空的,并没有触发新渲染,所以就在这里将无数据的那一条干掉
        rows.length > 0 ? $(".dataTables_empty").hide() : $(".dataTables_empty").show();

        const attributes = objectWithoutProperties(this.props, [
            'columns',
            'searching',
            'paging',
            'ordering',
            'order',
            'info',
            'retrieve',
            'dataArray',
            'buildRowOptions',
            'sortBy',
            'className',
            'withBoard'
        ]);

        let classes = "table table-hover table-striped w-100";

        classes += this.props.className ? ' ' + this.props.className : '';

        return (
            <table className={classes} ref={this.setRef} {...attributes}>
                <thead>
                <tr>
                    { headers }
                </tr>
                </thead>
                <tbody>
                    { rows }
                </tbody>
            </table>
        );
    }
}

export default Datatable;
