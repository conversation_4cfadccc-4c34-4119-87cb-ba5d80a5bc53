import React from 'react';
import {RAISE_COLOR, DROP_COLOR} from './Constants';
import {filterNumber} from '../../utils/helper';

export const renderChange = (change, type = 'percent', precision = 2, returnMode = 'str') => {
    if(change === '--') {
        return '<b>' + change + '</b>';
    }

    let value = type === 'percent' ? (parseFloat(change) * 100).toFixed(precision) + '%' : change;
    let color = change > 0 ? RAISE_COLOR : DROP_COLOR;
    let icon = change > 0 ? 'fa-arrow-up' : 'fa-arrow-down';
    let symbol = change > 0 ? '+' : '';

    if(returnMode === 'str') {
        return '<b style="color: ' + color + '">' + symbol + value + ' <i class="fa ' + icon + '"></i></b>';
    }

    return (
        <b style={{color: color}}>
            {symbol} {value}
            <i className={"fa " + icon}/>
        </b>
    );
};

export const renderValue = (value, type, precision = 2) => {
    if (value == "+∞" || type === "string") {
        return value;
    }
    if(type === 'percent') {
        return (parseFloat(value) * 100).toFixed(precision) + '%';
    } else if (type === 'kmbValue') {
        return filterNumber(value, true);
    } else if (type === 'string') {
        return value;
    }

    return parseFloat(value).toLocaleString('en-US', {maximumFractionDigits: precision})
    //minimumFractionDigits  为最少保留小数点数,所以选择用maximumFractionDigits
};

export const objectWithoutProperties = (obj, keys) => {
    var target = {};

    for (var i in obj) {
        if (keys.indexOf(i) >= 0) continue;
        if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;
        target[i] = obj[i];
    }

    return target;
};
