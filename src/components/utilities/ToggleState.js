import React from 'react';
import PropTypes from 'prop-types';
import $ from 'jquery';
import Storages from 'js-storage';

/** Handle states to/from local storage */
class StateTogglerStorage {
    static STORAGE_KEY_NAME = 'jq-toggleState';

    addState(classname) {
        let data = Storages.localStorage.get(StateTogglerStorage.STORAGE_KEY_NAME);
        if (data instanceof Array) data.push(classname);
        else data = [classname];
        Storages.localStorage.set(StateTogglerStorage.STORAGE_KEY_NAME, data);
    }

    removeState(classname) {
        let data = Storages.localStorage.get(StateTogglerStorage.STORAGE_KEY_NAME);
        if (data) {
            let index = data.indexOf(classname);
            if (index !== -1) data.splice(index, 1);
            Storages.localStorage.set(StateTogglerStorage.STORAGE_KEY_NAME, data);
        }
    }

    restoreState($elem) {
        let data = Storages.localStorage.get(StateTogglerStorage.STORAGE_KEY_NAME);
        if (data instanceof Array)
            $elem.addClass(data.join(' '));
    }
}

const StateToggler = props => {
    let prevClickHandler = props.onClick; // save the onClick of previous handler

    const toggle = new StateTogglerStorage();

    let classname = props.state;
    let target = props.target;
    let noPersist = !!props.nopersist;

    const $target = $(target);

    toggle.restoreState($target);

    const handleClick = e => {

        if (prevClickHandler) prevClickHandler.call();

        if (e.currentTarget.tagName === 'A') e.preventDefault();

        if (classname) {
            if ($target.hasClass(classname)) {
                $target.removeClass(classname);
                if (!noPersist)
                    toggle.removeState(classname);
            } else {
                $target.addClass(classname);
                if (!noPersist)
                    toggle.addState(classname);
            }
        }

        $(window).resize();

    };

    return (
        React.cloneElement(React.Children.only(props.children), {
            onClick: handleClick
        })
    )
};

StateToggler.propTypes = {
    onClick: PropTypes.func,
    state: PropTypes.string.isRequired,
    target: PropTypes.string,
    /** don't save to local storage */
    nopersist: PropTypes.bool,
    /** allows only one child element */
    children: PropTypes.element.isRequired
};

StateToggler.defaultProps = {
    target: 'body'
};

export default StateToggler;