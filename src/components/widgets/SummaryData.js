import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Row, Col } from 'reactstrap';
import { renderValue, renderChange } from '../utilities/Helpers'

class SummaryData extends Component {

    static propTypes = {
        data: PropTypes.arrayOf(PropTypes.shape({
            title: PropTypes.string.isRequired,
            value: PropTypes.oneOfType([
                PropTypes.string,
                PropTypes.number
            ]).isRequired,
            color: PropTypes.string,
            type: PropTypes.oneOf(['percent', 'value', 'string']),
            change: PropTypes.number, // 不是必须传递的
            changeType: PropTypes.oneOf(['percent', 'value']), //// 不是必须传递的
        })).isRequired
    };

    render() {
        let length = this.props.data.length;

        let md = 2;
        switch (length) {
            case 4:
                md = 3;
                break;
            case 3:
                md = 4;
                break;
        }

        return (
            <div className="card-summary-data">
                <Row>
                    {
                        this.props.data.map((item, i) => {
                            return (
                                <Col xs="12" sm="6" md={md} className="card-summary-item"  key={i}>
                                    <h3 className={item.color ? 'text-' + item.color : ''}>{renderValue(item.value, item.type || 'string')}</h3>
                                    <p>{item.change && renderChange(item.change, item.changeType, 2, 'jsx')} {item.title}</p>
                                </Col>
                            )
                        })
                    }
                </Row>
            </div>
        );
    }

}

export default SummaryData;
