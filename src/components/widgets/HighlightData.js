import React, { Component } from 'react';
import PropTypes from 'prop-types';
import * as CONST from '../utilities/Constants';
import Sparkline from '../charts/Sparklines';
import {Card, CardBody} from 'reactstrap';

class HighlightData extends Component {

    static propTypes = {
        value: PropTypes.string.isRequired,
        desc: PropTypes.string.isRequired,
        left: PropTypes.bool,
        sparkLine: PropTypes.shape({
            type: PropTypes.oneOf([
                'bar',
                'line'
            ]),
            values: PropTypes.oneOfType([
                PropTypes.string.isRequired,
                PropTypes.array.isRequired
            ]),
            color: PropTypes.string
        }),
        symbol: PropTypes.shape({
            icon: PropTypes.string,
            iconStyle: PropTypes.object
        })
    };

    state = {
        sparkLineOptions: {}
    };

    componentWillMount() {
        if(this.props.sparkLine)
            this.normalizeParams();
    }

    normalizeParams() {
        let { sparkLineOptions } = this.state;

        let color = this.props.sparkLine.color ? this.props.sparkLine.color : 'primary';

        if(this.props.sparkLine.type === 'line') {
            sparkLineOptions = {
                type: "line",
                height: "54",
                width: "60",
                lineWidth: "2",
                lineColor: CONST.APP_COLORS[color],
                spotColor: CONST.APP_COLORS['info'],
                fillColor: "",
                highlightSpotColor: CONST.APP_COLORS['danger'],
                highlightLineColor: CONST.APP_COLORS['gray'],
                spotRadius: "3",
            };
        }else{
            sparkLineOptions = {
                type: "bar",
                height: "54",
                barColor: CONST.APP_COLORS[color],
                barWidth:"3",
                barSpacing:"2",
            };

        }

        sparkLineOptions.disableHiddenCheck = true;
        sparkLineOptions.resize = true;

        this.setState({ sparkLineOptions });
    }


    render() {
        let contentClass = 'content';
        if(this.props.left) contentClass += ' left';
        return (
            <Card className="card-highlight-data">
                <CardBody>
                    {this.props.symbol && (
                        <div className="symbol">
                            <i className={this.props.symbol.icon} style={this.props.symbol.iconStyle} />
                        </div>
                        )
                    }

                    {this.props.sparkLine && (
                    <Sparkline
                        options={this.state.sparkLineOptions}
                        values={this.props.sparkLine.values}
                        className="sparklines"
                    />
                    )
                    }

                    {
                        this.props.icon && (
                            <div className="icon-src">
                                <img src={this.props.icon.src} />
                            </div>
                        )
                    }
                    <div className={contentClass}>
                        <h4 className="value">{this.props.value}</h4>
                        <p className="description">{this.props.desc}</p>
                    </div>
                </CardBody>
            </Card>
        );
    }

}

export default HighlightData;
