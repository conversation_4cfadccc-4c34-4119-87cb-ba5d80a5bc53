import React, { Component } from "react";
import PropTypes from "prop-types";
import { Button } from "reactstrap";
import Spinner from "../../components/widgets/Spinner";
import "parsleyjs/dist/i18n/zh_cn.js";
import "parsleyjs/dist/i18n/en.js";

class SubmitButton extends Component {
    static propTypes = {
        title: PropTypes.string.isRequired,
        submitting: PropTypes.bool,
    };

    setRef = (node) => (this.element = node);

    render() {
        let { title, submitting, ...rest } = this.props;

        return (
            <Button
                type="submit"
                innerRef={this.setRef}
                disabled={submitting}
                {...rest}
            >
                {submitting && <Spinner />}
                {!submitting && title}
            </Button>
        );
    }
}

export default SubmitButton;
