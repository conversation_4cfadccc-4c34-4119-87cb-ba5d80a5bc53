import React from 'react';
import PropTypes from 'prop-types';
import {ListGroup as LG, ListGroupItem} from 'reactstrap';


const ListGroup = props => {

    return (
        <LG>
            {
                props.list.map((item, i) => {

                    return (
                        <ListGroupItem className={(item.wrap || !item.label) ? '' : 'nowrap'} key={i}>
                            { item.label }
                            <span className="value" style={{width: !item.width ? 'auto': `${item.width}%`}}>{ typeof item.value === 'function' ? item.value() : item.value }</span>
                        </ListGroupItem>
                    );

                })
            }
        </LG>
    );

};

ListGroup.propTypes = {
    list: PropTypes.arrayOf(PropTypes.shape({
        value: PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.func
        ]),
        label: PropTypes.string,
        wrap: PropTypes.bool,
        width: PropTypes.number,
    }))
};

export default ListGroup;
