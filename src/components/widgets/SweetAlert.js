import React from 'react';
import PropTypes from 'prop-types';
import <PERSON>lert from 'sweetalert';

const SweetAlert = props => {

    const handleClick = e => {
        e.preventDefault();
        SAlert(props.options, p => props.callback(p, SweetAlert));
    };

    const { callback, ...rest } = props;
    return (
        <div {...rest} onClick={handleClick}>
            {props.children}
        </div>
    )
};

SweetAlert.propType = {
    options: PropTypes.object.isRequired,
    callback: PropTypes.func
};

SweetAlert.defaultProps = {
    callback: () => {}
};

export default SweetAlert;
