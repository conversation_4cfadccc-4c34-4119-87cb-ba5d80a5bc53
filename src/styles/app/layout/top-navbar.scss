/* ========================================================================
     Component: top-navbar
 ========================================================================== */

//
// Horizontal top navbar
// based on the bootstrap navbar styles and markup
// ----------------------------------------------------
// helper mixin to disable bs gradient mixin
@mixin gradient-remove() {
    background-image: none;
    background-repeat: no-repeat;
    filter: none;
}

$nav-header-wd:                 $aside-wd;
$nav-header-wd-toggled:         $aside-wd-collapsed;

$nav-header-wd-toggled-text:    $aside-wd-collapsed-text;

$nav-top-bg:                    $primary;
$nav-header-bg:                 transparent;

$nav-top-item-mobile:           $nav-top-bg;
$nav-top-item-mobile-active:    lighten($nav-top-item-mobile, 10%);

$nav-top-item-desktop:          #fff;
$nav-top-item-desktop-active:   $nav-top-item-mobile-active;


// Navbar top layout
.top-navbar {
    -webkit-backface-visibility: hidden; /* fixes chrome jump */
    margin-bottom: 0;
    border-radius: 0;
    z-index: 1050;
    border: 0;
    padding: 0;
    min-height: $navbar-height;
    background-color: $nav-top-bg;

    @include media-breakpoint-up(md) {
        .navbar-header {
            display: none;
        }
    }
}

// Navbar Mobile General styles
.top-navbar {
    position: relative;

    .navbar-header {
        background-color: $nav-header-bg;
    }

    .navbar-header {
        position: relative;
        z-index: 11;
        padding-left: 2.5rem;

        @include media-breakpoint-up(md) {
            padding-left: 0;
        } // Reset default to allow handle via different logo size
        .navbar-brand {
            padding: 0;
        } // Different Logo size depends on the navbar state
        .brand-logo {
            >img {
                margin: 0 auto;
                height: 28px;
            }
        }
        .brand-logo {
            display: block;
            padding: 10px 15px;
        }
    }

    .dropdown {
        position: static;
        .dropdown-menu {
            position: absolute;
            margin-top: 0;
            top: auto;
            left: 0;
            right: 0;
        }
    }
    @include media-breakpoint-up(lg) {
        .dropdown {
            position: relative;
            .dropdown-menu {
                top: $navbar-height - 1;
                left: 0;
                right: auto;
            }
            .dropdown-menu-right {
                right: 0;
                left: auto;
            }
        }
    }
    .navbar-nav>.nav-item>.navbar-text {
        color: $nav-top-item-desktop;
    }

    .navbar-nav>.nav-item>.nav-link {
        padding: 1.1rem 1.3rem;
        font-size: .85rem;
    }
    .navbar-nav>.nav-item>.nav-link,
    .navbar-nav>.nav-item.show>.nav-link {
        color: $nav-top-item-desktop;
        &:hover {
            color: $nav-top-item-desktop;
            background-color: transparent;
        }
    }
    .navbar-nav>.nav-item.active>.nav-link,
    .navbar-nav>.nav-item.show>.nav-link {
        &,
        &:hover {
            background-color: $nav-top-item-desktop-active;
        }
    }

    @include media-breakpoint-down(sm) {
        .navbar-text {
            margin: 10px;
        }
    }
}

@include media-breakpoint-down(sm) {
    .sidebar-toggle {
        position: absolute !important;
        top: 2px;
        left: 0;
        z-index: 3001; // Add color only for the icon
        >i {
            color: white;
        }
    }
}

// Navbar Desktop styles
@include media-breakpoint-up(md) {

    .top-navbar {

        margin-left: $nav-header-wd;

        .navbar-header {
            @include gradient-remove();
        } // Dropdown link open style
        .navbar-nav>a {
            box-shadow: 0 0 0 #000 inset;
            @include transition(all .2s);
        }

        .navbar-nav>.nav-item>.nav-link,
        .navbar-nav>.nav-item.show>.nav-link {
            color: $nav-top-item-desktop;
            &:hover {
                color: $nav-top-item-desktop;
                background-color: $nav-top-item-desktop-active;
            }
        } // Navbar link active style
        .navbar-nav>.nav-item.active>.nav-link,
        .navbar-nav>.nav-item.show>.nav-link {
            &,
            &:hover {
                background-color: $nav-top-item-desktop-active;
            }
        }
    }

    .aside-collapsed {
        .top-navbar {

            margin-left: $aside-wd-collapsed;

            .navbar-header {
                display: none;
            }

            .navbar-form {
                left: $nav-header-wd-toggled;
            }
        }
    }
}