.spinner {
    margin: 0 auto;
    width: 70px;
    text-align: center;
    > div {
        width: 8px;
        height: 8px;
        margin: 1px;
        background-color: #FFF;
        border-radius: 100%;
        display: inline-block;
        animation: spinner-bouncedelay 1.4s infinite ease-in-out both;
    }
    .bounce1 {
        animation-delay: -0.32s;
    }
    .bounce2 {
        animation-delay: -0.16s;
    }
}

@keyframes spinner-bouncedelay {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1.0);
    }
}

.spinner-folding-symbol {
    margin: 20px auto;
    width: 40px;
    height: 40px;
    position: relative;
    transform: rotateZ(45deg);
    .spinner-symbol {
        float: left;
        width: 50%;
        height: 50%;
        position: relative;
        transform: scale(1.1);
        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: $danger;
            border-radius: 10px 0 0 0;
            animation: spinner-foldSymbolAngle 2.4s infinite linear both;
            transform-origin: 100% 100%;
        }
        &:after {
            content: '';
            position: absolute;
            bottom: -1px;
            right: -1px;
            width: 45%;
            height: 45%;
            border-radius: 3px 0 0 0;
            background-color: #FFF;
            transform-origin: 100% 100%;
        }
    }

    .spinner-symbol2 {
        transform: scale(1.1) rotateZ(90deg);
        &:before {
            animation-delay: 0.3s;
        }
    }

    .spinner-symbol3 {
        transform: scale(1.1) rotateZ(180deg);
        &:before {
            animation-delay: 0.6s;
        }
    }

    .spinner-symbol4 {
        transform: scale(1.1) rotateZ(270deg);
        &:before {
            animation-delay: 0.9s;
        }
    }
}

@keyframes spinner-foldSymbolAngle {
    0%, 10% {
        transform: perspective(140px) rotateX(-180deg);
        opacity: 0;
    }
    25%, 75% {
        transform: perspective(140px) rotateX(0deg);
        opacity: 1;
    }
    90%, 100% {
        transform: perspective(140px) rotateY(180deg);
        opacity: 0;
    }
}