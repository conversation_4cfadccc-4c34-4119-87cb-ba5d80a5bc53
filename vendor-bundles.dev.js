const webpack = require( 'webpack');
const path = require('path');
const packageJson = require('./package');

const dllDir = "./modelengine/" + packageJson.name + "/dllVendor";

module.exports = {
  entry: {
    'vendor': [
      "react",
      "react-dom",
      "react-router",
        "jquery",
        "toastr"
    ]
  },
  devtool: '#source-map',
  output: {
    path: path.join(__dirname, dllDir),
    filename: '[name].dll.js',
    library: '[name]'
  },

  plugins: [
    new webpack.DllPlugin({
      path: path.join(__dirname, dllDir, '[name]-manifest.json'),
      name: '[name]'
    })
  ],
};
