body {
  line-height: 1.5;
}
html,
body {
  position: relative;
  height: 100%;
  margin: 0;
  padding: 0;
}

#fullpage {
  width: 100%;
  height: 100%;
  margin-left: auto;
  margin-right: auto;
}
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
ol {
  padding-left: 1em;
}
p {
  margin: 0;
  padding: 0;
}
:focus {
  outline: 0;
}
a {
  text-decoration: none;
  cursor: pointer;
}
.swiper-slide {
  display: flex;
  align-items: center;
  overflow: hidden;
}
.swiper-slide,
.swiper-slide .slide {
  text-align: center;
  font: 15px "Microsoft Yahei";
  color: #fff;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.swiper-slide .content {
  max-width: 75%;
  margin: 0 auto;
  width: 100%;
}
/*head-nav*/
.head-page-box {
  height: 60px;
  position: fixed;
  z-index: 99;
  width: 100%;
  top: 0px;
  left: 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 100px;
  box-sizing: border-box;
}
.head-page-box .logo {
  /* height: 20px; */
  height: 40px;
}
ul#nav li {
  float: left;
  font-size: 15px;
  color: #ffffff;
}
ul#nav li > a {
  color: #ffffff;
  text-shadow: 0px 0px 4px #333;
  font-weight: 500;
  padding: 0px 20px 0px;
  display: inline-block;
  height: 60px;
  line-height: 60px;
}
ul#nav li:hover a,
ul#nav li.active a {
  color: #1fcbfb;
  text-shadow: 0px 0px 0px #333;
}

.login {
  margin-left: 50px;
  position: relative;
  cursor: pointer;
}
.login > a::after {
  content: ">";
  transform: rotate(90deg) scaleY(2);
  display: inline-block;
  margin-left: 10px;
  font-size: 13px;
}
.login .login-modal {
  display: none;
  cursor: auto;
}
.login:hover .login-modal {
  display: block;
}
ul#nav li.login:hover,
ul#nav li.login:hover a {
  background: #154c9c;
  color: #ffffff;
}
.login .login-modal {
  position: absolute;
  right: -30px;
  width: 390px;
  background: #154c9c;
  padding: 22px 28px;
  font-size: 11px;
  color: #ffffff;
}
.login .login-modal > div:first-child {
  color: #5bffc8;
  font-size: 12px;
  margin-bottom: 17px;
}
.login .login-modal .name {
  margin-bottom: 9px;
  color: #a5e4ff;
}
.login .login-modal .arr {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 9px;
}
.login .login-modal .arr > div {
  border: 1px solid rgba(113, 191, 239, 0.7);
  border-radius: 6px;
  text-align: center;
  padding: 21px 0 10px;
}
.login .login-modal .arr > div img {
  max-width: 80%;
  margin: 0 auto;
  margin-top: 20px;
}
.login .login-modal .arr.arr1 > div img {
  width: 52px;
  margin-bottom: 10px;
}

#fullPage-nav ul li a.active span,
.fp-slidesNav ul li a.active span {
  background: #fff;
  opacity: 0.8;
}
#fullPage-nav ul li a span,
.fp-slidesNav ul li a span {
  border: 1px solid #fff;
  opacity: 0.8;
}
.controlArrow {
  display: none;
}
@keyframes fly {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(13px);
  }
  to {
    transform: translateY(0);
  }
}
/* ltx 首屏 */
.ltx-index h3.title > div {
  color: #fff;
  font-size: 67px;
  padding: 0 0 12px;
  animation: fly 3s ease 0.4s infinite;
}
.ltx-index p {
  animation: fly 3s ease 1.9s infinite;
}
/* msh 首屏 */
.msh-digital {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  transform: translateY(25%);
  color: #fff;
  height: 100%;
}
.msh-digital .title {
  border-bottom: 1px solid #fff;
  display: inline-block;
  font-size: 67px;
  padding: 0 0 12px;
  margin-bottom: 30px;
}
.msh-digital img {
  width: 336px;
  animation: fly 3s ease 0.4s infinite;
  margin-right: 150px;
}
.msh-digital > div {
  animation: fly 3s ease 1.9s infinite;
  margin-bottom: 5%;
}
/*  */
.layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.layout > .content {
  flex: 1;
  justify-content: center;
  display: flex;
  flex-direction: column;
}
/* footer */
.footer {
  background: #111213;
  color: #666666;
  font-size: 13px;
}
.footer a {
  color: #666666;
}
.footer .top {
  display: flex;
  justify-content: space-between;
  text-align: left;
  padding: 22px 0 6px;
}
.footer .top .left .link {
  margin-bottom: 20px;
}
.footer .top .left .link a {
  display: inline-block;
}
.footer .top .left .link a:first-child {
  padding-right: 20px;
}
.footer .top .left .link a :last-child {
  padding-left: 20px;
}
.footer .top .left .link a + a {
  border-left: 1px solid #666;
  padding: 0 20px;
}
.footer .top .left .address {
  line-height: 2;
  font-size: 12px;
}
.footer .top .right {
  line-height: 2;
  font-size: 12px;
}
.footer .bottom {
  padding: 9px 0;
  background: #212223;
  text-align: center;
}
/* index */
.ltx-index h3.title,
.home .swiper-slide .title {
  border-bottom: 1px solid #fff;
  display: inline-block;
  font-size: 32px;
  line-height: 1;
  padding: 0 0 18px;
  margin-bottom: 18px;
}
.home .slide-view {
  position: relative;
  bottom: 100px;
  z-index: 999;
  display: flex;
  flex-direction: row;
}
.home .slide-view .swiper-pagination {
  width: auto;
  left: 0;
}
.home .slide-view > div:last-child {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  width: 178px;
}
.home .slide-view .swiper-button-prev,
.home .slide-view .swiper-button-next {
  position: unset;
  color: #fff;
  width: auto;
  height: auto;
}
.home .slide-view .swiper-button-prev::after,
.home .slide-view .swiper-button-next::after {
  font-size: 16px;
  width: auto;
  text-shadow: 0px 0px 4px #000;
}
.home .slide-view .swiper-button-prev::after {
  content: "上一页";
  display: inline-block;
}
.home .slide-view .swiper-button-next::after {
  content: "下一页";
  display: inline-block;
}
.home .slide-view .swiper-pagination-bullet {
  display: inline-block;
  vertical-align: top;
  width: 50px;
  height: 3px;
  border-radius: 0;
  background-color: hsla(0, 0%, 100%, 0.5);
  box-shadow: 0px 0px 4px 0px hsla(0, 0%, 0%, 0.5);
}
.home .slide-view .swiper-pagination-bullet-active {
  background-color: #fff;
}
.home .section1 {
  font-size: 17px;
}
.home .section1 .title {
  font-size: 67px;
}
.home .section1 .slide-1 .content {
  position: relative;
  width: 100%;
  height: 100%;
}
.home .section1 .slide-1 .center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.home .section1 .slide-1 .center img {
  opacity: 0;
}
@keyframes logo {
  0% {
    opacity: 0;
    left: 180px;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    left: 180px;
    transform: scale(1);
  }
  to {
    opacity: 1;
    left: 0;
    transform: scale(1);
  }
}
.home .section1 .slide-1 .center .logo {
  position: relative;
  left: 180px;
  height: 80px;
  margin-right: 20px;
  animation: logo 1s ease-in 0.1s forwards;
}
@keyframes logo-name {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
.home .section1 .slide-1 .center .name {
  height: 80px;
  animation: logo-name 0.5s ease 0.7s forwards;
}
@keyframes logo-line {
  0% {
    transform: scaleX(0);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}
.home .section1 .slide-1 .center .line {
  width: 380px;
  animation: logo-line 0.5s ease 1.2s forwards;
}
@keyframes logo-slogon {
  0% {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.home .section1 .slide-1 .center .slogon {
  width: 380px;
  margin-top: 20px;
  animation: logo-slogon 0.5s ease 1.8s forwards;
}
@keyframes slide-img-show {
  0% {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
@keyframes slide-img {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    opacity: 1;
    transform: translateY(-20px);
  }
}
.home .section1 .slide-1 .content > img {
  position: absolute;
  transform: scale(0);
}
.home .section1 .slide-1 .content > img.slide1-1 {
  left: 0;
  top: 120px;
  width: 159px;
  animation: slide-img-show 0.5s ease 2.3s forwards,
    slide-img 3s linear 2.8s infinite alternate;
}
.home .section1 .slide-1 .content > img.slide1-2 {
  left: 116px;
  bottom: 38px;
  width: 118px;
  animation: slide-img-show 0.5s ease 2.8s forwards,
    slide-img 3s ease 3.5s infinite alternate;
}
.home .section1 .slide-1 .content > img.slide1-3 {
  right: 0;
  top: 90px;
  width: 169px;
  animation: slide-img-show 0.5s ease 3.5s forwards,
    slide-img 3s ease-out 4.2s infinite alternate;
}
.home .section1 .slide-1 .content > img.slide1-4 {
  right: 0;
  bottom: 38px;
  width: 180px;
  animation: slide-img-show 0.5s ease 3.3s forwards,
    slide-img 3s ease-in 3.6s infinite alternate;
}
.home .section2 p {
  margin-bottom: 37px;
}
.home #service {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-gap: 7px 11px;
  margin-bottom: 26px;
}
.home #service > a {
  text-align: center;
  background-color: #439ee0;
  padding: 13px 0;
  line-height: 1;
  border-radius: 11px;
}
.home #service > a:hover {
  background-color: #a1cf11;
}
.home .section2 .mode {
  display: flex;
  justify-content: space-between;
}
.home .section2 .mode .text-img {
  display: flex;
  justify-content: space-around;
  flex-direction: column;
}
.home .section2 .mode .text-img > div {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.home .section2 .mode .text-img > div > img {
  width: 50px;
  margin-left: 25px;
  transition: transform 0.35s;
  transform: rotate(0);
}
.home .section2 .mode .text-img > div > img:hover {
  transition: transform 0.35s;
  transform: rotate(360deg);
}
.home .section2 .mode .text-img:last-child > div {
  flex-direction: row-reverse;
}
.home .section2 .mode .text-img:last-child > div > img {
  margin: 0 25px 0 0;
}
.home .section2 .mode .img > img {
  width: 193px;
  height: 193px;
}
.home .products {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 24px;
}
.home .products a {
  color: #fff;
}
.home .products a:hover {
  color: #1fcbfb;
}
.home .products > div > div {
  border-radius: 11px;
  padding: 12px 12px 23px;
  margin-bottom: 28px;
  cursor: pointer;
  position: relative;
  z-index: 1;
}
.home .products > div > div::before {
  content: "";
  position: absolute;
  background-color: rgba(137, 176, 255, 0.25);
  box-shadow: 0px 1px 2px 6px rgba(0, 0, 0, 0);
  border-radius: 11px;
  z-index: -1;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 13px;
  transition: all 0.35s;
}
.home .products > div > div:hover::before {
  background-color: rgba(137, 176, 255, 0.65);
  height: 116%;
  top: -8%;
  transition: all 0.35s;
}
.home .swiper-slide .product-title {
  margin-bottom: 64px;
}
.home .products .pro-name {
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 19px;
}
.home .products .pro-desc {
  font-size: 12px;
  margin-bottom: 19px;
}
.home .products .pro-item {
  display: grid;
  grid-gap: 17px 28px;
  font-size: 14px;
}
.home .products .pro-item > div {
  padding: 14px 0 11px;
  position: relative;
  margin-top: 26px;
  cursor: pointer;
  border-radius: 11px;
}
.home .products .pro-item > div > .icon {
  position: absolute;
  top: -26px;
  left: 50%;
  padding: 11px;
  width: 36px;
  height: 36px;
  transform: translateX(-50%);
  border-radius: 50%;
  box-sizing: border-box;
}
.home .products .pro-item > div .icon::before {
  content: "";
  width: 14px;
  height: 14px;
  transition: transform 0.35s;
  transform: rotate(0);
  display: inline-block;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}

.icon.zndd::before {
  background-image: url(/public/img/index/icon/zndd.png);
}
.icon.jhzf::before {
  background-image: url(/public/img/index/icon/jhzf.png);
}
.icon.xsyx::before {
  background-image: url(/public/img/index/icon/xsyx.png);
}
.icon.tzgl::before {
  background-image: url(/public/img/index/icon/tzgl.png);
}
.icon.ydzf::before {
  background-image: url(/public/img/index/icon/ydzf.png);
}
.icon.fxpj::before {
  background-image: url(/public/img/index/icon/fxpj.png);
}
.icon.xxgs::before {
  background-image: url(/public/img/index/icon/xxgs.png);
}
.icon.mlcf::before {
  background-image: url(/public/img/index/icon/mlcf.png);
}
.icon.qyls::before {
  background-image: url(/public/img/index/icon/qyls.png);
}
.icon.qyzcsj::before {
  background-image: url(/public/img/index/icon/qyzcsj.png);
}
.icon.qycbsj::before {
  background-image: url(/public/img/index/icon/qycbsj.png);
}
.icon.qyjydhy::before {
  background-image: url(/public/img/index/icon/qyjydhy.png);
}
.icon.qytxsj::before {
  background-image: url(/public/img/index/icon/qytxsj.png);
}
.icon.gssfsj::before {
  background-image: url(/public/img/index/icon/gssfsj.png);
}
.home .products .pro-item > div:hover .icon::before {
  transition: transform 0.35s;
  transform: rotate(360deg);
}
.home .products .pro-item#msh {
  grid-template-columns: repeat(4, 1fr);
}
.home .products .pro-item#msh > div,
.home .products .pro-item#msh > div > .icon {
  background: rgba(51, 118, 255, 0.72);
}
.home .products .pro-item#ltx {
  grid-template-columns: repeat(3, 1fr);
}
.home .products .pro-item#ltx > div,
.home .products .pro-item#ltx > div > .icon {
  background: rgba(31, 203, 251, 0.72);
}
.home .resolve {
  display: flex;
  justify-content: space-between;
  text-align: left;
}
.home .resolve > div {
  width: 30%;
  cursor: pointer;
}
.home .resolve .res-title {
  margin-bottom: 7px;
}
.home .resolve .res-img {
  position: relative;
  margin-bottom: 15px;
  overflow: hidden;
  cursor: pointer;
}
.home .resolve .res-img > img {
  width: 100%;
  transition: transform 0.35s;
  transform: scale(1);
  display: block;
}
.home .resolve .res-img:hover > img {
  transition: transform 0.35s;
  transform: scale(1.1);
}
.home .resolve .res-img > span {
  position: absolute;
  width: 100%;
  padding: 6px;
  font-size: 12px;
  display: block;
  background: rgba(0, 0, 0, 0.6);
  box-sizing: border-box;
  bottom: 0;
}
.home .resolve .res-item {
  display: flex;
  font-size: 12px;
  margin-bottom: 11px;
}
.home .resolve .res-item:hover {
  color: #1fcbfb;
}
.home .resolve .res-item > span:first-child {
  margin-right: 17px;
  min-width: 7em;
  max-width: 7em;
  box-sizing: border-box;
}
/* ltx msh */
.ltx .swiper-slide,
.msh .swiper-slide {
  color: #333333;
}
.ltx .title {
  font-weight: 600;
  line-height: 1;
  margin-bottom: 12px;
  font-size: 24px;
}
.ltx .section1 p {
  color: #fff;
}
.ltx .section2 .desc {
  font-size: 17px;
  font-weight: 400;
  line-height: 2;
  margin-bottom: 40px;
}
.ltx .section3 .title {
  margin-bottom: 90px;
}
.ltx .product {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 22px;
  text-align: left;
}
.ltx .product .pro-item {
  background: #ffffff;
  padding: 23px;
  cursor: pointer;
}
.ltx .product .pro-item:hover {
  background-color: #e9efff;
}
.ltx .product .pro-item .icon img {
  width: 30px;
  margin-bottom: 11px;
}
.ltx .product .pro-item .pro-name {
  font-size: 15px;
  margin-bottom: 7px;
  font-weight: 600;
}
.ltx .product .pro-item .pro-desc {
  font-size: 11px;
  margin-bottom: 7px;
  font-weight: 600;
}
.ltx .product .pro-item .pro-sub-desc {
  font-size: 11px;
  margin-bottom: 15px;
  color: #999;
}
.ltx .product .pro-item ol {
  color: #999;
  font-size: 11px;
  margin: 0;
}
.ltx .product .pro-item ol li + li {
  margin-top: 11px;
}

.ltx .section4 .title,
.msh .section4 .title {
  margin-bottom: 34px;
}
.ltx .advantage {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  text-align: left;
  background: #78caed;
  color: #e7f0ff;
  cursor: pointer;
}
.ltx .advantage .adv-item + .adv-item {
  border-left: 1px solid #fff;
}
.ltx .advantage .adv-item {
  padding: 34px 13px;
}
.ltx .advantage .adv-item .adv-name {
  font-size: 17px;
  margin-bottom: 17px;
}
.ltx .advantage .adv-item .adv-desc {
  font-size: 11px;
  visibility: hidden;
  transform: translateY(-28px);
}
.ltx .advantage .adv-item:hover {
  background: #3b72ff;
}
.ltx .advantage .adv-item .icon img {
  width: 22px;
}
.ltx .advantage .adv-item:hover .adv-desc {
  visibility: visible;
}
.ltx .advantage .adv-item:hover .icon {
  visibility: hidden;
}
/* msh */
.msh .section4 .title {
  font-size: 24px;
}
@keyframes scorll {
  0% {
    transform: translateY(50%);
    opacity: 0;
  }
  50% {
    opacity: 0;
    transform: translateY(50%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.msh .section2 .scorll {
  position: relative;
  bottom: -100px;
  opacity: 0;
  overflow: hidden;
  margin-bottom: 30px;
  font-size: 17px;
  color: #333333;
  transition: all 1s;
}
.msh .section2.active .scorll {
  bottom: 0;
  opacity: 1;
}
.msh .section2 .scorll .title {
  font-size: 32px;
  color: #307eeb;
  margin-bottom: 18px;
}
.msh .section3 .title {
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 46px;
}
.msh .section3 .tab {
  display: flex;
  justify-content: center;
  margin-bottom: 36px;
}
.msh .section3 .tab li {
  background-color: #fff;
  padding: 8px 33px;
  border-radius: 12px;
  border: 1px solid #f4f4f4;
  cursor: pointer;
}
.msh .section3 .tab li.active,
.msh .section3 .tab li:hover {
  background: #d8f1ff;
  border: 1px solid #d8f1ff;
}
.msh .section3 .tab li + li {
  margin-left: 86px;
}
.msh .section3 .pro-content {
  display: none;
}
.msh .section3 .pro-content.active {
  display: block;
}
.msh .section3 .pro-content .desc {
  font-size: 20px;
  color: #333333;
  margin-bottom: 20px;
}
.msh .section3 .pro-content .tips {
  font-size: 12px;
  color: #333333;
  margin-bottom: 20px;
}
.msh .section3 .pro-content .product {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  margin-bottom: 40px;
}
.msh .section3 .pro-content.pc.active {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 170px;
  text-align: left;
}
.msh .section3 .pro-content.pc .desc {
  margin-top: 33px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
  color: #020202;
  text-align: left;
}
.msh .section3 .pro-content.pc .desc img {
  width: 43px;
  margin-right: 28px;
}
.msh .section3 .pro-content.pc .hr {
  display: inline-block;
  width: 2px;
  height: 8px;
  background-color: #2f88ff;
  margin-right: 3px;
}
.msh .section3 .pro-content.pc ul li + li {
  margin-top: 12px;
}
.msh .section3 .pro-content.pc ul li .name {
  font-size: 12px;
  color: #020202;
  margin-bottom: 7px;
}
.msh .section3 .pro-content.pc ul li p {
  font-size: 11px;
  color: #666666;
}
.msh .section3 .product .name {
  font-size: 18px;
  font-weight: 400;
  color: #333333;
  margin-bottom: 11px;
}
.msh .section3 .product .desc {
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  margin: 0;
}
.msh .section3 .product ul {
  display: flex;
  justify-content: space-around;
  flex-direction: column;
}
.msh .section3 .product ul li {
  display: flex;
  align-items: center;
}
.msh .section3 .product ul li .icon {
  padding: 21px;
  width: 74px;
  height: 74px;
  background-color: #fff;
  border-radius: 50%;
  box-sizing: border-box;
  cursor: pointer;
}
.msh .section3 .product ul li.active .icon,
.msh .section3 .product ul li:hover .icon {
  background-color: #d8f1ff;
}
.msh .section3 .product ul li .icon img {
  width: 30px;
  height: 30px;
}
.msh .section3 .product ul:first-child li {
  text-align: right;
  justify-content: end;
}
.msh .section3 .product ul:first-child li .icon {
  margin-left: 20px;
}
.msh .section3 .product ul:last-child li {
  text-align: left;
  justify-content: start;
  flex-direction: row-reverse;
}
.msh .section3 .product ul:last-child li .icon {
  margin-right: 20px;
}
.msh .section3 .product .img-w {
  position: relative;
  margin: 0 auto;
  height: max-content;
}
.msh .section3 .product .img-w::before {
  content: "";
  display: block;
  width: calc(100% - 10px);
  max-width: 200px;
  height: 100%;
  position: absolute;
  box-shadow: 0px 0px 80px 4px rgba(0, 0, 0, 0.3);
  border-radius: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.msh .section3 .product .img {
  padding: 10px 15px 11px;
  max-width: 200px;
  box-sizing: content-box;
  background-image: url(/public/img/msh/tab-img/bg.png);
  background-repeat: no-repeat;
  background-position: center 0;
  background-size: contain;
}
.msh .section3 .product .img img {
  width: 100%;
  display: none;
}
.msh .section3 .product .img img.active {
  display: block;
}
.msh .advantage {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 59px;
  font-size: 15px;
  color: #333333;
}
.msh .advantage .adv-item .img-box {
  width: 100%;
  position: relative;
  border-radius: 11px;
  overflow: hidden;
  color: #ffffff;
  cursor: pointer;
  text-align: left;
  margin-bottom: 13px;
}
.msh .advantage .adv-item .img-box img {
  width: 100%;
}
.msh .advantage .adv-item .cover {
  position: absolute;
  bottom: -100%;
  height: 100%;
  width: 100%;
  padding: 17px;
  background: linear-gradient(180deg, #4abef0 0%, #0072ff 100%);
  box-sizing: border-box;
  font-size: 15px;
}
.msh .advantage .adv-item .cover .adv-name {
  text-align: center;
  font-size: 24px;
  margin-bottom: 16px;
}
.msh .advantage .adv-item:hover .cover {
  bottom: 0;
  transition: bottom 0.5s;
}

/* about */
@keyframes flyIn {
  to {
    opacity: 1;
    left: 0;
  }
}
.about .swiper-slide {
  text-align: left;
}
.about .swiper-slide .content .badge {
  position: relative;
  margin-bottom: 12px;
  opacity: 0;
  left: 200px;
}

.about .swiper-slide .content > .title {
  position: relative;
  opacity: 0;
  left: 200px;
}
.about .swiper-slide.active .content .badge {
  animation: flyIn 1.5s ease forwards;
}
.about .swiper-slide.active .content > .title {
  animation: flyIn 1.5s ease 0.1s forwards;
}
.about .swiper-slide .content .badge::before {
  content: "企乐汇征信有限公司";
  background: #1470cc;
  padding: 0 4px;
  font-size: 11px;
  line-height: 22px;
  height: 22px;
  display: inline-block;
}
.about .swiper-slide .content .badge::after {
  content: "";
  position: absolute;
  right: -22px;
  top: 1px;
  width: 0;
  height: 0;
  border: 11px solid transparent;
  border-left-color: #1470cc;
  display: inline-block;
}
.about .swiper-slide .title {
  font-size: 27px;
  font-weight: bold;
  margin: 13px 0;
}
@keyframes rollIn {
  0% {
    opacity: 0;
    transform: translateX(-100%) rotate(-120deg);
  }
  to {
    opacity: 1;
    transform: translateX(0) rotate(0deg);
  }
}

@keyframes rollOut {
  0% {
    opacity: 1;
    transform: translateX(0) rotate(0deg);
  }
  to {
    opacity: 0;
    transform: translateX(100%) rotate(120deg);
  }
}

.about .enp-info > span {
  display: inline-block;
  background: url("/public/img/about/bg.png") no-repeat;
  background-size: contain;
  width: 80px;
  height: 80px;
  padding: 60px 20px 0;
  animation-timing-function: ease-in-out;
  animation-duration: 1s;
  margin: 20px;
  opacity: 0;
  animation-fill-mode: both;
}
.about .swiper-slide.active .enp-info > span {
  animation-name: rollIn;
}
.about .enp-info > span[data-id-1] {
  animation-delay: 1s;
}
.about .enp-info > span[data-id-2] {
  animation-delay: 1.3s;
}
.about .enp-info > span[data-id-3] {
  animation-delay: 1.6s;
}
/* .about .enp-info > span[data-id-4] {
  animation-delay: 1.9s;
} */
.about .section1 .desc {
  opacity: 0;
}
.about .section1 .desc p {
  text-indent: 2em;
}
.about .section1.active .desc {
  opacity: 1;
  transition: opacity 0.5s 1.9s;
}
.about .section2 .desc {
  font-size: 18px;
  font-weight: 600;
  margin: 32px 0;
  opacity: 0;
}
.about .section2.active .desc {
  opacity: 1;
  transition: opacity 0.5s 1s;
}
.about .section2 .desc p + p {
  margin-top: 13px;
}

.about .enp-culture {
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
  opacity: 0;
}
.about .section2.active .enp-culture {
  opacity: 1;
  transition: opacity 0.5s 1s;
}

.about .enp-culture > div {
  width: 29%;
  margin-right: 5%;
  position: relative;
}
.about .enp-culture > div img {
  width: 100%;
}
.about .enp-culture > div > div {
  position: absolute;
  bottom: 20px;
  text-align: center;
  color: #000;
  width: 100%;
}
.about .enp-culture > div > div .title {
  font-size: 18px;
  font-weight: bold;
  color: #1470cc;
}
.about .section3 .progress {
  max-width: 440px;
  margin: 0 auto;
  opacity: 0;
}
.about .section3.active .progress {
  opacity: 1;
  transition: opacity 0.5s 1s;
}
.about .section3 .progress > div {
  display: flex;
  justify-content: center;
}
.about .section3 .progress > div > div:first-child,
.about .section3 .progress > div > div:last-child {
  width: 300%;
}
.about .section3 .progress > div > div:first-child {
  text-align: right;
}
.about .section3 .progress > div img {
  width: 15px;
}
.about .section3 .progress > div p {
  font-size: 12px;
  padding: 10px;
}
.about .section3 .progress > div span {
  display: inline-block;
  font-size: 17px;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  margin: 0 39px;
  padding: 3px 15px;
  border-radius: 7px;
}
.about .section4 .about-address {
  width: max-content;
  display: inline-flex;
  margin-left: 43px;
  background-color: rgba(255, 255, 255, 0.6);
  padding: 48px 103px 27px 0;
  color: #000000;
  font-size: 14px;
  align-items: center;
  opacity: 0;
  transform: translateY(-20px);
}
.about .section4.active .about-address {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 1s, transform 0.5s;
}
.about .section4 .about-address img {
  width: 276px;
  position: relative;
  left: -43px;
  margin-right: -20px;
  margin-top: 8px;
}
.about .section4 .about-address .phone,
.about .section4 .about-address .mail {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 8px;
}
.about .section4 .about-address p {
  margin-bottom: 8px;
}
