$(function () {
  // 服务客群
  const service = [
    "餐饮",
    "零售",
    "商超便利店",
    "生鲜水果",
    "服饰服装",
    "商业综合体",
    "本地生活",
    "休闲娱乐",
    "运动健身",
    "丽人美体",
    "酒店住宿",
    "汽修养护",
  ];
  service.map((item) => {
    $("#service").append(`<a>${item}</a>`);
  });
  // 明星产品 - 码上汇
  const msh = [
    { text: "智能点单", icon: "zndd" },
    { text: "聚合支付", icon: "jhzf" },
    { text: "线上营销", icon: "xsyx" },
    { text: "台账管理", icon: "tzgl" },
    { text: "移动执法", icon: "ydzf" },
    { text: "风险评级", icon: "fxpj" },
    { text: "信息公示", icon: "xxgs" },
    { text: "明亮厨房", icon: "mlcf" },
  ];
  msh.map((item) => {
    $("#msh").append(`
        <div>
            <div class='icon ${item.icon}'></div>
            <span>${item.text}</span>
        </div>
    `);
  });
  // 明星产品 - 乐添信
  const ltx = [
    { text: "企业流水数据", icon: "qyls" },
    { text: "企业资产数据", icon: "qyzcsj" },
    { text: "企业成本数据", icon: "qycbsj" },
    { text: "企业经营地核验", icon: "qyjydhy" },
    { text: "企业通讯数据", icon: "qytxsj" },
    { text: "工商司法数据", icon: "gssfsj" },
  ];
  ltx.map((item) => {
    $("#ltx").append(`
         <div>
            <div class='icon ${item.icon}'></div>
            <span>${item.text}</span>
        </div>
    `);
  });
  var swiper = new Swiper("#fullpage", {
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    direction: "vertical",
    slidesPerView: "auto",
    spaceBetween: 0,
    speed: 800, //设置切换速度
    mousewheel: { forceToAxis: true },
    keyboard: true,
  });
  var swiper2 = new Swiper(".section1", {
    pagination: {
      el: ".swiper-pagination",
    },
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
    slidesPerView: 1,
    spaceBetween: 0,
    speed: 800, //设置切换速度
    loop: true, // 循环模式选项
    autoplay: {
      delay: 5000,
      stopOnLastSlide: false,
      disableOnInteraction: true,
    },
  });
});
