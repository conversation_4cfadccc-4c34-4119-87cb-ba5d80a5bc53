const webpack = require('webpack');
const HtmlWebpackPlugin =require('html-webpack-plugin');
const autoprefixer =require('autoprefixer');
const path =require('path');
const AddAssetHtmlPlugin =require('add-asset-html-webpack-plugin');
const packageJson =require('./package');
const configuration = require('./configuration');
const distDir = "./modelengine/" + packageJson.name;
const dllDir = "./modelengine/" + packageJson.name + "/dllVendor";
const dllManifest = require(path.join(__dirname, dllDir, '/vendor-manifest.json'));
process.noDeprecation = true;
module.exports = {
    mode: 'development',
  //These options were replaced by a single option resolve.modules. See resolving for more usage.
  resolve: {
    extensions: ['.js', '.jsx', '.json', ".scss", '.css'],
    alias: {
        '@': path.resolve(__dirname, 'src')
    }
  },
  devtool: 'cheap-module-eval-source-map', // more info:https://webpack.github.io/docs/build-performance.html#sourcemaps and https://webpack.github.io/docs/configuration.html#devtool
  entry: [
      './src/webpack-public-path',
      path.resolve(__dirname, 'src/index.js')
  ],
  // Defining path seems necessary for this to work consistently on Windows machines.
  target: 'web', // necessary per https://webpack.github.io/docs/testing.html#compile-and-test
  output: {
    path: path.resolve(__dirname, distDir), // Note: Physical files are only output by the production build task `npm run build`.
    publicPath: '/',
    filename: distDir + "/[name].js",
  },
  devServer: {
        port: 8079,
        host: 'localhost',
        hot: true,
        compress:false,
        historyApiFallback: true,  //不跳转
        openPage: 'statics/login',
        overlay: true,
        inline: true,  //实时刷新,
        proxy: {
            '/api/*': {
                target: 'https://punch-decision-warpdrive-in-qa-01.76hui.com',
                changeOrigin: true,
                secure:false
            }
        }
},
  plugins: [
    new webpack.LoaderOptionsPlugin({
      debug: true
    }),
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify('development'), // Tells React to build in either dev or prod modes. https://facebook.github.io/react/downloads.html (See bottom)
      __DEV__: true,
        'globalConfig': JSON.stringify(configuration)
    }),
    new webpack.HotModuleReplacementPlugin(),
    new webpack.ProvidePlugin({
      '$': "jquery",
      'jQuery': "jquery",
      'window.jQuery': "jquery",
      'window.$': 'jquery'
    }),
    new webpack.DllReferencePlugin({
      context: path.join(__dirname),
      manifest: dllManifest
    }),
    new HtmlWebpackPlugin({     // Create HTML file that includes references to bundled CSS and JS.
      template: 'src/index.ejs',
      minify: {
        removeComments: true,
      },
      inject: true,
      remoteDebugger: ""
    }),
    new AddAssetHtmlPlugin({
      filepath: require.resolve(path.join(__dirname, dllDir, dllManifest.name + ".dll.js")),
      includeSourcemap: false,
      hash: true,
      outputPath: path.resolve(__dirname, distDir + '/dll'), // Note: Physical files are only output by the production build task `npm run build`.
      publicPath: '/dll',
    })
  ],
  module: {
    rules: [
      {test: /\.jsx?$/, exclude: /node_modules/, use: {
          loader:"babel-loader",
          options: {
              presets: ['@babel/preset-react'],
              plugins: ['@babel/plugin-proposal-class-properties']
          }
        }
      },
      {test: /\.eot(\?v=\d+.\d+.\d+)?$/, use: ["file-loader"]},
      {
        test: /\.woff(2)?(\?v=[0-9]\.[0-9]\.[0-9])?$/,
        use: [{
          loader: "url-loader",
          options: {
            "limit": 10000,
            "mimetype": "application/font-woff"
          }
        }]
      },
      {
        test: /\.otf(\?v=\d+\.\d+\.\d+)?$/,
        use: [{
          loader: "url-loader",
          options: {
            "limit": 10000,
            "mimetype": "application/x-font-otf",
            "name": "modelengine/[name].[ext]"
          }
        }]
      },
      {
        test: /\.ttf(\?v=\d+\.\d+\.\d+)?$/,
        use: [{
          loader: "url-loader",
          options: {
            "limit": 10000,
            "mimetype": "application/octet-stream"
          }
        }]
      },
      {
        test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
        use: [{
          loader: "url-loader",
          options: {
            "limit": 10000,
            "mimetype": "image/svg+xml"
          }
        }]
      },
      {
        test: /\.txt$/,
        use: [{
          loader: "file-loader",
          options: {
            "name": "[name].[ext]"
          }
        }]
      },
      {
        test: /\.(jpe?g|png|gif)$/i,
        use: [{
          loader: "file-loader",
          options: {
            "name": "[path][name].[ext]"
          }
        }]
      },
      {
        test: /\.ico$/,
        use: [{
          loader: "file-loader",
          options: {
            "name": "[path][name].[ext]"
          }
        }]
      },
      {
        test: /(\.css|\.scss)$/,
        use: ["style-loader", {
          loader: "css-loader",
          options: {
            sourceMap: true
          }
        }, {
          loader: "postcss-loader",
          options: {
            "plugins": ()=> [autoprefixer]
          }
        }, {
          loader: "sass-loader",
          options: {
            "sourceMap": true
          }
        }]
      }
    ]
  }
};
