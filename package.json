{"name": "modelengine", "version": "0.1.0", "description": "", "engines": {"npm": ">=3"}, "scripts": {"preinstall": "node tools/nodeVersionCheck.js", "setup": "node tools/setup/setupMessage.js && npm install && node tools/setup/setup.js", "remove-demo": "babel-node tools/removeDemo.js", "start-message": "babel-node tools/startMessage.js", "prestart": "npm-run-all --parallel start-message remove-statics", "start": "npm-run-all --parallel open:src lint:watch", "open:src": "./node_modules/.bin/webpack --config vendor-bundles.dev.js && webpack-dev-server --open --hot --config webpack.config.dev.js", "lint": "esw webpack.config.* src tools --color", "lint:watch": "npm run lint -- --watch", "clean-statics": "npm run remove-statics && mkdir static", "clean-tar": "npm run remove-tar && mkdir tar", "remove-statics": "rimraf ./static", "remove-tar": "rimraf ./tar", "prebuild": "npm run clean-statics", "build": "babel-node ./node_modules/.bin/webpack --config vendor-bundles.prod.js && babel-node tools/build.js", "deploy": "babel-node ./tools/removeStatic.js && babel-node ./node_modules/.bin/webpack --config vendor-bundles.prod.js && babel-node tools/build.js", "remove-dllVendor": "rimraf ./static/dllVendor", "analyze-bundle": "babel-node ./tools/analyzeBundle.js"}, "author": "pintec-ued", "license": "MIT", "dependencies": {"animate.css": "3.5.2", "antd": "^3.13.0", "bootstrap": "4.1.1", "datatables.net": "^1.12.1", "datatables.net-bs": "^1.12.1", "datatables.net-bs4": "^1.12.1", "datatables.net-responsive": "^2.3.0", "datatables.net-responsive-bs": "^2.3.0", "jquery": "3.6.1", "loader-utils": "1.1.0", "lodash": "4.17.11", "lodash.omit": "^4.5.0", "memoize-one": "4.0.3", "metismenu": "^2.5.0", "mini-css-extract-plugin": "^0.5.0", "modernizr": "^3.6.0", "moment": "^2.24.0", "node-sass": "^4.14.1", "prop-types": "^15.6.2", "raphael": "2.2.7", "react": "16.2.0", "react-autocomplete": "^1.8.1", "react-checkbox-tree": "1.4.1", "react-datetime": "2.14.0", "react-day-picker": "6.2.1", "react-dom": "16.2.0", "react-dropzone": "^4.2.9", "react-router": "4.2.0", "react-router-bootstrap": "0.24.4", "react-router-dom": "4.2.2", "react-select": "1.2.1", "react-switch-button": "2.2.2", "react-toastify": "4.0.0-rc.4", "react-transition-group": "2.2.1", "reactstrap": "^6.0.1", "sweetalert": "^2.1.2", "toastr": "2.1.2", "whatwg-fetch": "2.0.0"}, "devDependencies": {"@babel/cli": "7.2.3", "@babel/core": "7.2.2", "@babel/node": "7.2.2", "@babel/plugin-proposal-class-properties": "7.2.3", "@babel/plugin-transform-react-constant-elements": "7.2.0", "@babel/plugin-transform-react-display-name": "7.2.0", "@babel/preset-env": "7.2.3", "@babel/preset-react": "7.0.0", "@babel/preset-stage-1": "7.0.0", "@babel/register": "7.0.0", "add-asset-html-webpack-plugin": "3.1.2", "autoprefixer": "6.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "babel-loader": "7.1.5", "babel-plugin-transform-react-remove-prop-types": "0.4.21", "bootstrap-daterangepicker": "3.0.3", "chalk": "1.1.3", "css-loader": "0.28.11", "eslint": "4.19.1", "eslint-plugin-import": "^2.15.0", "eslint-plugin-jsx-a11y": "6.1.2", "eslint-plugin-react": "7.7.0", "eslint-watch": "4.0.2", "event-stream": "^4.0.1", "extract-text-webpack-plugin": "4.0.0-beta.0", "file-loader": "3.0.1", "font-awesome": "^4.7.0", "highcharts": "^6.1.1", "history": "4.7.2", "html-webpack-plugin": "3.2.0", "imports-loader": "0.8.0", "isparta": "4.0.0", "jquery-slimscroll": "1.3.8", "jquery-sparkline": "2.4.0", "jquery-steps": "1.1.0", "js-storage": "1.0.1", "node-sass-chokidar": "^1.4.0", "npm-run-all": "3.1.1", "open": "0.0.5", "optimize-css-assets-webpack-plugin": "5.0.1", "parsleyjs": "^2.7.1", "postcss-loader": "3.0.0", "rc-pagination": "1.12.11", "rc-slider": "8.6.1", "react-masonry-component": "6.2.1", "regenerator-runtime": "0.13.1", "rimraf": "2.5.4", "sass-loader": "^8.0.2", "style-loader": "0.20.3", "uglifyjs-webpack-plugin": "2.1.1", "url-loader": "0.5.7", "webpack": "4.28.4", "webpack-bundle-analyzer": "2.11.1", "webpack-cli": "3.2.1", "webpack-dev-server": "3.1.14", "webpack-hot-middleware": "2.24.3"}, "keywords": [], "repository": {"type": "git", "url": ""}}