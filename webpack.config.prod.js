// For info about this file refer to webpack and webpack-hot-middleware documentation
// For info on how we're generating bundles with hashed filenames for cache busting: https://medium.com/@okonetchnikov/long-term-caching-of-static-assets-with-webpack-1ecb139adb95#.w99i89nsz
const webpack = require('webpack');
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const HtmlWebpackPlugin =require('html-webpack-plugin');
const autoprefixer =require('autoprefixer');
const path =require('path');
const AddAssetHtmlPlugin =require('add-asset-html-webpack-plugin');
const OptimizeCssAssetsPlugin =require('optimize-css-assets-webpack-plugin');
const packageJson =require('./package');
const TerserPlugin = require('terser-webpack-plugin')

const distDir = "static/" + packageJson.name;
const dllDir = "./static/" + packageJson.name + "/dllVendor";

const env = process.env.NODE_ENV || "/";
let static_path = "/modelengine";

const GLOBALS = {
  'process.env.NODE_ENV': JSON.stringify('production'),
  __DEV__: false
};

const dllManifest = require(path.join(__dirname, dllDir, '/vendor-manifest.json'));

module.exports = {
    mode: 'production',
   resolve: {
    extensions: ['.js', '.jsx', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  entry: {
    index: path.resolve(__dirname, 'src/index.js')
  },
  target: 'web', // necessary per https://webpack.github.io/docs/testing.html#compile-and-test
  output: {
    path: path.resolve(__dirname, distDir),
    publicPath: `${static_path}/`,
    filename: 'modelengine/[name].[chunkhash].js',
  },
  plugins: [
    // Hash the files using MD5 so that their names change when the content changes.

    // Tells React to build in prod mode. https://facebook.github.io/react/downloads.html
    new webpack.DefinePlugin(GLOBALS),

    // Generate an external css file with a hash in the filename
    new MiniCssExtractPlugin({
      // Options similar to the same options in webpackOptions.output
      // both options are optional
      filename: "modelengine/[name].css",
      chunkFilename: "modelengine/index-[hash].css"
  }),

    new webpack.ProvidePlugin({
      '$': "jquery",
      'jQuery': "jquery",
      'window.jQuery': "jquery",
      'window.$': 'jquery'

    }),

    new OptimizeCssAssetsPlugin({
      assetNameRegExp: /\.css$/g,
      cssProcessor: require('cssnano'),
      cssProcessorOptions: { discardComments: {removeAll: true } },
      canPrint: true
    }),

    new webpack.DllReferencePlugin({
      context: path.join(__dirname),
      manifest: dllManifest
    }),
    // Generate HTML file that contains references to generated bundles. See here for how this works: https://github.com/ampedandwired/html-webpack-plugin#basic-usage
    new HtmlWebpackPlugin({
      template: 'src/index.ejs',
      filename: 'index.html',
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true,
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true
      },
      inject: true,
      // Note that you can add custom options here if you need to handle other custom logic in index.html
      // To track JavaScript errors via TrackJS, sign up for a free trial at TrackJS.com and enter your token below.
      trackJSToken: ''
    }),
    new AddAssetHtmlPlugin({
      filepath: require.resolve(path.join(__dirname, dllDir, dllManifest.name + ".dll.js")),
      outputPath: "/modelengine/dll",
      publicPath: `${static_path}/modelengine/dll`,
      includeSourcemap: false
    }),
  ],
  optimization: {
    minimize: true, //取代 new UglifyJsPlugin(/* ... */)
    minimizer:
        (env === "development"
        ? []
        : [
            new TerserPlugin({
                terserOptions: {
                    output: {
                        comments: false,
                    },
                },
                extractComments: false,
            }),
        ]),
    providedExports: true,
    usedExports: true,
    //识别package.json中的sideEffects以剔除无用的模块，用来做tree-shake
    //依赖于optimization.providedExports和optimization.usedExports
    sideEffects: true,
    //取代 new webpack.optimize.ModuleConcatenationPlugin()
    concatenateModules: true,
    //取代 new webpack.NoEmitOnErrorsPlugin()，编译错误时不打印输出资源。
    noEmitOnErrors: true
},
  module: {
    rules: [
      {test: /\.jsx?$/, exclude: /node_modules/, use: ["babel-loader"]},
      {
        test: /\.eot(\?v=\d+.\d+.\d+)?$/, use: [{
        loader: "url-loader",
        options: {
          name: "[name].[ext]"
        }
      }]
      },
      {
        test: /\.woff(2)?(\?v=[0-9]\.[0-9]\.[0-9])?$/,
        use: [{
          loader: "url-loader",
          options: {
            "limit": 10000,
            "mimetype": "application/font-woff",
            "name": "modelengine/[name].[ext]"
          }
        }]
      },
      {
        test: /\.otf(\?v=\d+\.\d+\.\d+)?$/,
        use: [{
          loader: "url-loader",
          options: {
            "limit": 10000,
            "mimetype": "application/x-font-otf",
            "name": "modelengine/[name].[ext]"
          }
        }]
      },
      {
        test: /\.ttf(\?v=\d+\.\d+\.\d+)?$/,
        use: [{
          loader: "url-loader",
          options: {
            "limit": 10000,
            "mimetype": "application/octet-stream",
            "name": "modelengine/[name].[ext]"
          }
        }]
      },
      {
        test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
        use: [{
          loader: "url-loader",
          options: {
            "limit": 10000,
            "mimetype": "image/svg+xml",
            "name": "modelengine/[name].[ext]"
          }
        }]
      },
      {
        test: /\.txt$/,
        use: [{
          loader: "file-loader",
          options: {
            "name": "modelengine/[name].[ext]"
          }
        }]
      },
      {
        test: /\.(jpe?g|png|gif)$/i,
        use: [{
          loader: "file-loader",
          options: {
            "name": "modelengine/[hash].[ext]"
          }
        }]
      },
      {
        test: /\.ico$/,
        use: [{
          loader: "file-loader",
          options: {
            "name": "modelengine/[name].[ext]"
          }
        }]
      },
      {
        test: /(\.css|\.scss)$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader
          },
          'css-loader',
          {
            loader: "postcss-loader",
            options: {
              "plugins": ()=> [autoprefixer]
            }
          },
          'sass-loader',
        ]
    }
    ]
  }
};
