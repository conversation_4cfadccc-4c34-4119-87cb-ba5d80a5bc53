const webpack = require( 'webpack');
const path = require('path');
const packageJson = require('./package');

const dllDir = "./static/" + packageJson.name + "/dllVendor";

const GLOBALS = {
  'process.env.NODE_ENV': JSON.stringify('production'),
  __DEV__: false
};

module.exports = {
    entry: {
      'vendor': [
        "react",
        "react-dom",
        "react-router",
        "jquery",
        "toastr"
      ]
    },
  output: {
    path: path.join(__dirname, dllDir),
    filename: '[name]_[hash].dll.js',
    library: '[name]_[hash]'
  },

  plugins: [
    new webpack.DefinePlugin(GLOBALS),
    new webpack.DllPlugin({
      path: path.join(__dirname, dllDir, '[name]-manifest.json'),
      name: '[name]_[hash]'
    })
  ]
};
