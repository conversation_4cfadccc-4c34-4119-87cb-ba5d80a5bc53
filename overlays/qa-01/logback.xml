<?xml version="1.0" encoding="UTF-8"?>
<configuration>

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
      <jsonGeneratorDecorator class="net.logstash.logback.decorate.FeatureJsonGeneratorDecorator"/>
      <providers>
        <pattern>
          <pattern>
            {
            "date": "%date{yyyy-MM-dd HH:mm:ss.SSS}",
            "level": "%level",
            "trace": "%X{mdc.trace}",
            "thread": "%thread",
            "class": "%logger",
            "line": "%line",
            "message": "%message",
            "stack": "%ex"
            }
          </pattern>
        </pattern>
      </providers>
    </encoder>
  </appender>

  <root level="INFO">
    <appender-ref ref="STDOUT" />
  </root>

</configuration>
