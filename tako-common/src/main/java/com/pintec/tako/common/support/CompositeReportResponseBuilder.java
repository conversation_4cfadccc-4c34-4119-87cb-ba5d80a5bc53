package com.pintec.tako.common.support;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 复合报告响应构建器工具类
 * 提供便捷的方法来构建结构化的复合报告响应
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
public class CompositeReportResponseBuilder {

    /**
     * 从TakoController的处理结果构建ApiResponse
     * 
     * @param futures CompletableFuture列表
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @param startTime 开始时间
     * @param partialSuccessEnabled 是否启用部分成功模式
     * @param timeoutSeconds 超时配置
     * @return 包含CompositeReportResponse数据的ApiResponse
     */
    public static ApiResponse<Map<String, ?>> buildApiResponse(
            java.util.List<CompletableFuture<ReportResult>> futures,
            int successCount,
            int failureCount,
            long startTime,
            boolean partialSuccessEnabled,
            int timeoutSeconds) {
        
        long endTime = System.currentTimeMillis();
        int totalCount = futures.size();
        
        // 构建结构化响应
        CompositeReportResponse.Builder builder = CompositeReportResponse.builder()
            .setBasicStats(totalCount, successCount, failureCount)
            .setTimeStats(startTime, endTime)
            .setConfigInfo(partialSuccessEnabled, timeoutSeconds);
        
        // 收集成功和失败的结果
        for (CompletableFuture<ReportResult> future : futures) {
            try {
                ReportResult result = future.getNow(null);
                if (result != null) {
                    if (result.isSuccess()) {
                        // 由于我们无法直接访问ReportResult，我们需要在调用处传入结果
                        // 这里只是框架，具体的数据需要在调用处设置
                    } else {
                        String errorMsg = result.getError() != null ? 
                            result.getError().getMessage() : "未知错误";
                        builder.addError(result.getReportType(), errorMsg);
                    }
                }
            } catch (Exception e) {
                log.warn("获取报告结果异常", e);
            }
        }
        
        CompositeReportResponse compositeResponse = builder.build();
        
        // 转换为向后兼容的平铺格式，包含元数据
        Map<String, Object> responseData = compositeResponse.toFlattenedDataWithMetadata();
        
        // 根据结果确定返回状态
        if (partialSuccessEnabled) {
            if (successCount > 0) {
                return ApiResponse.success(responseData);
            } else {
                ApiResponse<Map<String, ?>> errorResponse = ApiResponse.sysError("所有报告处理均失败");
                errorResponse.setData(responseData);
                return errorResponse;
            }
        } else {
            if (failureCount == 0) {
                return ApiResponse.success(responseData);
            } else {
                ApiResponse<Map<String, ?>> errorResponse = ApiResponse.sysError("部分报告处理失败");
                errorResponse.setData(responseData);
                return errorResponse;
            }
        }
    }

    /**
     * 从聚合的结果数据构建ApiResponse
     * 这是一个更直接的方法，适用于已经收集好数据的场景
     */
    public static ApiResponse<Map<String, ?>> buildFromAggregatedData(
            Map<String, Object> successResults,
            Map<String, String> errorResults,
            int totalCount,
            int successCount,
            int failureCount,
            long startTime,
            long endTime,
            boolean partialSuccessEnabled,
            int timeoutSeconds) {
        
        CompositeReportResponse.Builder builder = CompositeReportResponse.builder()
            .setBasicStats(totalCount, successCount, failureCount)
            .setTimeStats(startTime, endTime)
            .setConfigInfo(partialSuccessEnabled, timeoutSeconds);
        
        // 添加成功的数据
        builder.addAllSuccessData(successResults);
        
        // 添加错误信息
        if (errorResults != null) {
            for (Map.Entry<String, String> entry : errorResults.entrySet()) {
                builder.addError(entry.getKey(), entry.getValue());
            }
        }
        
        CompositeReportResponse compositeResponse = builder.build();
        
        // 转换为向后兼容的平铺格式，包含元数据
        Map<String, Object> responseData = compositeResponse.toFlattenedDataWithMetadata();
        
        // 根据结果确定返回状态
        if (partialSuccessEnabled) {
            if (successCount > 0) {
                return ApiResponse.success(responseData);
            } else {
                ApiResponse<Map<String, ?>> errorResponse = ApiResponse.sysError("所有报告处理均失败");
                errorResponse.setData(responseData);
                return errorResponse;
            }
        } else {
            if (failureCount == 0) {
                return ApiResponse.success(responseData);
            } else {
                ApiResponse<Map<String, ?>> errorResponse = ApiResponse.sysError("部分报告处理失败");
                errorResponse.setData(responseData);
                return errorResponse;
            }
        }
    }

    /**
     * 构建纯结构化响应（不包装在ApiResponse中）
     * 适用于需要直接使用CompositeReportResponse的场景
     */
    public static CompositeReportResponse buildStructuredResponse(
            Map<String, Object> successResults,
            Map<String, String> errorResults,
            int totalCount,
            int successCount,
            int failureCount,
            long startTime,
            long endTime,
            boolean partialSuccessEnabled,
            int timeoutSeconds) {
        
        CompositeReportResponse.Builder builder = CompositeReportResponse.builder()
            .setBasicStats(totalCount, successCount, failureCount)
            .setTimeStats(startTime, endTime)
            .setConfigInfo(partialSuccessEnabled, timeoutSeconds);
        
        // 添加成功的数据
        builder.addAllSuccessData(successResults);
        
        // 添加错误信息
        if (errorResults != null) {
            for (Map.Entry<String, String> entry : errorResults.entrySet()) {
                builder.addError(entry.getKey(), entry.getValue());
            }
        }
        
        return builder.build();
    }

    /**
     * 为单个reportType创建简单响应
     * 用于向后兼容单reportType的场景
     */
    public static ApiResponse<Map<String, ?>> buildSingleReportResponse(
            Map<String, Object> reportData, 
            String reportType,
            long costTime) {
        
        if (reportData == null || reportData.isEmpty()) {
            return ApiResponse.sysError("报告处理失败");
        }
        
        // 对于单reportType，直接返回数据，不需要复杂的结构
        return ApiResponse.success(reportData);
    }

    /**
     * ReportResult内部类定义（用于类型安全）
     * 这应该与TakoController中的ReportResult保持一致
     */
    public static class ReportResult {
        private final String reportType;
        private final Map<String, Object> data;
        private final Throwable error;

        public ReportResult(String reportType, Map<String, Object> data, Throwable error) {
            this.reportType = reportType;
            this.data = data;
            this.error = error;
        }

        public boolean isSuccess() {
            return error == null && data != null;
        }

        public String getReportType() {
            return reportType;
        }

        public Map<String, Object> getData() {
            return data != null ? data : new HashMap<>();
        }

        public Throwable getError() {
            return error;
        }
    }
} 