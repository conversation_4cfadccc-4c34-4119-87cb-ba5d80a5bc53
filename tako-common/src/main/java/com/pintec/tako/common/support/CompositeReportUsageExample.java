package com.pintec.tako.common.support;

import java.util.HashMap;
import java.util.Map;

/**
 * 复合报告响应结构使用示例
 * 展示如何正确使用CompositeReportResponse和CompositeReportResponseBuilder
 * 
 * <AUTHOR> Assistant
 */
public class CompositeReportUsageExample {

    /**
     * 示例1：构建包含成功和失败报告的复合响应
     */
    public static void exampleMixedResults() {
        // 模拟成功的报告数据
        Map<String, Object> successData = new HashMap<>();
        successData.put("reportA_result", "成功数据A");
        successData.put("reportB_result", "成功数据B");
        
        // 模拟失败的报告错误
        Map<String, String> errorData = new HashMap<>();
        errorData.put("reportC", "连接超时");
        errorData.put("reportD", "数据源不可用");
        
        // 构建响应
        long startTime = System.currentTimeMillis() - 5000; // 5秒前
        long endTime = System.currentTimeMillis();
        
        CompositeReportResponse response = CompositeReportResponse.builder()
            .addAllSuccessData(successData)
            .addError("reportC", "连接超时")
            .addError("reportD", "数据源不可用")
            .setBasicStats(4, 2, 2)
            .setTimeStats(startTime, endTime)
            .setConfigInfo(true, 30)
            .build();
        
        // 输出不同格式的响应
        System.out.println("=== 结构化响应 ===");
        System.out.println("成功数据: " + response.getData());
        System.out.println("错误信息: " + response.getErrors());
        System.out.println("元数据: " + response.getMetadata());
        
        System.out.println("\n=== 平铺格式（向后兼容） ===");
        Map<String, Object> flattenedData = response.toFlattenedDataWithMetadata();
        System.out.println("平铺响应: " + flattenedData);
        
        System.out.println("\n=== 状态判断 ===");
        System.out.println("是否完全成功: " + response.isCompleteSuccess());
        System.out.println("是否部分成功: " + response.isPartialSuccess());
        System.out.println("是否完全失败: " + response.isCompleteFailure());
    }

    /**
     * 示例2：使用CompositeReportResponseBuilder构建ApiResponse
     */
    public static void exampleApiResponseBuilding() {
        // 模拟TakoController中的聚合数据
        Map<String, Object> successResults = new HashMap<>();
        
        Map<String, Object> report1Data = new HashMap<>();
        report1Data.put("score", 85);
        report1Data.put("status", "正常");
        successResults.put("report1", report1Data);
        
        Map<String, Object> report2Data = new HashMap<>();
        report2Data.put("risk_level", "低");
        report2Data.put("confidence", 0.95);
        successResults.put("report2", report2Data);
        
        Map<String, String> errorResults = new HashMap<>();
        errorResults.put("report3", "API限流");
        
        // 构建ApiResponse
        ApiResponse<Map<String, ?>> apiResponse = CompositeReportResponseBuilder.buildFromAggregatedData(
            successResults,
            errorResults,
            3, // totalCount
            2, // successCount  
            1, // failureCount
            System.currentTimeMillis() - 3000, // startTime
            System.currentTimeMillis(), // endTime
            true, // partialSuccessEnabled
            25 // timeoutSeconds
        );
        
        System.out.println("=== ApiResponse结果 ===");
        System.out.println("状态码: " + apiResponse.getCode());
        System.out.println("消息: " + apiResponse.getMessage());
        System.out.println("数据: " + apiResponse.getData());
    }

    /**
     * 示例3：单reportType的简单响应
     */
    public static void exampleSingleReportResponse() {
        Map<String, Object> singleReportData = new HashMap<>();
        singleReportData.put("user_score", 92);
        singleReportData.put("risk_assessment", "低风险");
        
        ApiResponse<Map<String, ?>> response = CompositeReportResponseBuilder.buildSingleReportResponse(
            singleReportData,
            "creditScore",
            1500L // costTime
        );
        
        System.out.println("=== 单报告响应 ===");
        System.out.println("响应: " + response);
    }

    /**
     * 示例4：JSON序列化格式展示
     */
    public static void exampleJsonSerialization() {
        Map<String, Object> bankScoreData = new HashMap<>();
        bankScoreData.put("score", 750);
        bankScoreData.put("level", "优秀");
        
        Map<String, Object> riskAnalysisData = new HashMap<>();
        riskAnalysisData.put("risk", "低");
        riskAnalysisData.put("probability", 0.15);
        
        Map<String, Object> allSuccessData = new HashMap<>();
        allSuccessData.put("bankScore", bankScoreData);
        allSuccessData.put("riskAnalysis", riskAnalysisData);
        
        CompositeReportResponse response = CompositeReportResponse.builder()
            .addAllSuccessData(allSuccessData)
            .addError("thirdPartyCheck", "第三方服务暂时不可用")
            .setBasicStats(3, 2, 1)
            .setTimeStats(System.currentTimeMillis() - 2000, System.currentTimeMillis())
            .setConfigInfo(true, 20)
            .build();
        
        System.out.println("=== JSON序列化示例 ===");
        System.out.println("结构化格式:");
        System.out.println("{\n" +
            "  \"data\": " + response.getData() + ",\n" +
            "  \"errors\": " + response.getErrors() + ",\n" +
            "  \"metadata\": {\n" +
            "    \"totalCount\": " + response.getMetadata().getTotalCount() + ",\n" +
            "    \"successCount\": " + response.getMetadata().getSuccessCount() + ",\n" +
            "    \"failureCount\": " + response.getMetadata().getFailureCount() + ",\n" +
            "    \"costTimeMs\": " + response.getMetadata().getCostTimeMs() + ",\n" +
            "    \"successRate\": " + response.getMetadata().getSuccessRate() + ",\n" +
            "    \"partialSuccessEnabled\": " + response.getMetadata().getPartialSuccessEnabled() + "\n" +
            "  }\n" +
            "}");
        
        System.out.println("\n向后兼容格式:");
        Map<String, Object> compatible = response.toFlattenedDataWithMetadata();
        System.out.println(compatible);
    }

    public static void main(String[] args) {
        System.out.println("========== CompositeReportResponse 使用示例 ==========\n");
        
        exampleMixedResults();
        System.out.println("\n" + "============================================================" + "\n");
        
        exampleApiResponseBuilding();
        System.out.println("\n" + "============================================================" + "\n");
        
        exampleSingleReportResponse();
        System.out.println("\n" + "============================================================" + "\n");
        
        exampleJsonSerialization();
    }
} 