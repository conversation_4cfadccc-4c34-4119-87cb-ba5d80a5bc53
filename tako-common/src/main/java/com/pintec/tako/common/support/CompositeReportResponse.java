package com.pintec.tako.common.support;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

/**
 * 复合报告响应结构
 * 支持多reportType并发处理的结构化响应，包含成功数据、错误信息和执行元数据
 * 
 * <AUTHOR> Assistant
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CompositeReportResponse {

    /**
     * 成功的报告数据
     * key: reportType, value: 报告处理结果
     */
    private Map<String, Object> data;

    /**
     * 失败的报告错误信息
     * key: reportType, value: 错误信息
     */
    private Map<String, String> errors;

    /**
     * 执行元数据统计信息
     */
    private ExecutionMetadata metadata;

    /**
     * 执行元数据内部类
     */
    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ExecutionMetadata {
        /**
         * 报告总数
         */
        private Integer totalCount;

        /**
         * 成功处理的报告数
         */
        private Integer successCount;

        /**
         * 失败的报告数
         */
        private Integer failureCount;

        /**
         * 总耗时（毫秒）
         */
        private Long costTimeMs;

        /**
         * 请求开始时间戳
         */
        private Long startTime;

        /**
         * 请求结束时间戳
         */
        private Long endTime;

        /**
         * 平均单个报告处理时间（毫秒）
         */
        private Double averageCostTimeMs;

        /**
         * 成功率（百分比）
         */
        private Double successRate;

        /**
         * 是否启用了部分成功模式
         */
        private Boolean partialSuccessEnabled;

        /**
         * 使用的超时配置（秒）
         */
        private Integer timeoutSeconds;
    }

    /**
     * 构建器模式创建响应对象
     */
    public static class Builder {
        private final CompositeReportResponse response;

        public Builder() {
            this.response = new CompositeReportResponse();
            this.response.data = new HashMap<>();
            this.response.errors = new HashMap<>();
            this.response.metadata = new ExecutionMetadata();
        }

        /**
         * 添加成功的报告数据
         */
        public Builder addSuccessData(String reportType, Map<String, Object> reportData) {
            if (reportData != null && !reportData.isEmpty()) {
                this.response.data.putAll(reportData);
            }
            return this;
        }

        /**
         * 批量添加成功的报告数据
         */
        public Builder addAllSuccessData(Map<String, Object> allSuccessData) {
            if (allSuccessData != null && !allSuccessData.isEmpty()) {
                this.response.data.putAll(allSuccessData);
            }
            return this;
        }

        /**
         * 添加失败的报告错误信息
         */
        public Builder addError(String reportType, String errorMessage) {
            if (errorMessage != null && !errorMessage.trim().isEmpty()) {
                this.response.errors.put(reportType, errorMessage);
            }
            return this;
        }

        /**
         * 设置基础统计信息
         */
        public Builder setBasicStats(int totalCount, int successCount, int failureCount) {
            this.response.metadata.totalCount = totalCount;
            this.response.metadata.successCount = successCount;
            this.response.metadata.failureCount = failureCount;
            
            // 计算成功率
            if (totalCount > 0) {
                this.response.metadata.successRate = (double) successCount / totalCount * 100;
            }
            
            return this;
        }

        /**
         * 设置时间统计信息
         */
        public Builder setTimeStats(long startTime, long endTime) {
            this.response.metadata.startTime = startTime;
            this.response.metadata.endTime = endTime;
            this.response.metadata.costTimeMs = endTime - startTime;
            
            // 计算平均处理时间
            if (response.metadata.totalCount != null && response.metadata.totalCount > 0) {
                this.response.metadata.averageCostTimeMs = 
                    (double) this.response.metadata.costTimeMs / response.metadata.totalCount;
            }
            
            return this;
        }

        /**
         * 设置配置信息
         */
        public Builder setConfigInfo(boolean partialSuccessEnabled, int timeoutSeconds) {
            this.response.metadata.partialSuccessEnabled = partialSuccessEnabled;
            this.response.metadata.timeoutSeconds = timeoutSeconds;
            return this;
        }

        /**
         * 构建最终响应对象
         */
        public CompositeReportResponse build() {
            // 如果没有错误信息，则不返回errors字段
            if (response.errors.isEmpty()) {
                response.errors = null;
            }
            
            return response;
        }
    }

    /**
     * 创建构建器实例
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 将复合响应转换为向后兼容的平铺格式
     * 用于支持只关心数据而不关心错误和元数据的客户端
     * 
     * @return 平铺的数据Map，包含所有成功的报告数据
     */
    public Map<String, Object> toFlattenedData() {
        Map<String, Object> flattened = new HashMap<>();
        if (data != null) {
            flattened.putAll(data);
        }
        return flattened;
    }

    /**
     * 将复合响应转换为包含元数据的平铺格式
     * 在平铺数据的基础上添加_metadata字段
     * 
     * @return 包含数据和元数据的Map
     */
    public Map<String, Object> toFlattenedDataWithMetadata() {
        Map<String, Object> flattened = toFlattenedData();
        
        // 构建元数据Map
        Map<String, Object> metadataMap = new HashMap<>();
        if (metadata != null) {
            if (metadata.totalCount != null) metadataMap.put("totalCount", metadata.totalCount);
            if (metadata.successCount != null) metadataMap.put("successCount", metadata.successCount);
            if (metadata.failureCount != null) metadataMap.put("failureCount", metadata.failureCount);
            if (metadata.costTimeMs != null) metadataMap.put("costTimeMs", metadata.costTimeMs);
            if (metadata.successRate != null) metadataMap.put("successRate", metadata.successRate);
            
            if (errors != null && !errors.isEmpty()) {
                metadataMap.put("errors", errors);
            }
        }
        
        if (!metadataMap.isEmpty()) {
            flattened.put("_metadata", metadataMap);
        }
        
        return flattened;
    }

    /**
     * 判断是否包含成功的数据
     */
    public boolean hasSuccessData() {
        return data != null && !data.isEmpty();
    }

    /**
     * 判断是否包含错误信息
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }

    /**
     * 判断是否为完全成功（所有报告都成功）
     */
    public boolean isCompleteSuccess() {
        return metadata != null && 
               metadata.failureCount != null && 
               metadata.failureCount == 0 &&
               hasSuccessData();
    }

    /**
     * 判断是否为部分成功（有成功也有失败）
     */
    public boolean isPartialSuccess() {
        return hasSuccessData() && hasErrors();
    }

    /**
     * 判断是否为完全失败（所有报告都失败）
     */
    public boolean isCompleteFailure() {
        return !hasSuccessData() && hasErrors();
    }
} 