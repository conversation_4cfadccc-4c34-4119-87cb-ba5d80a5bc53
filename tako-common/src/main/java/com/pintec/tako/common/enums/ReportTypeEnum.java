package com.pintec.tako.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by wenxuan.ding on 2022/8/5 16:18.
 */
@Getter
@AllArgsConstructor
public enum ReportTypeEnum {
    BaiRong_BizAffiPersSearch("bizaffiperssearch", "关联关系核验", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_BizAffiPersSearch, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-08-10 14:26:00"),
    BaiRong_BlackXian<PERSON>ao("blackXianGao", "BR-限高被执行人(B3410002)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_BlackXianGao, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-24 10:54:00"),
    BaiRong_BlackXianGao2("blackXianGao2", "BR-限高被执行人2(B3410007)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_BlackXianGao2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-25 10:05:00"),
    BaiRong_BlackJieDai("blackJieDai", "BR-借贷意向验证(B3410003)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_BlackJieDai, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-25 11:02:00"),

    BaiRong_black_jiedai_sha256("bairong_black_jiedai_sha256", "BR-借贷意向验证_SHA256", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_black_jiedai_sha256, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-10-26 19:02:00"),
    BaiRong_scorewis("bairong_scorewis", "BR-融智分", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_scorewis, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-20 15:42:00"),

    BaiRong_Black_Teshu("bairong_black_teshu", "BY-特殊名单验证(B3410001)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_Black_Teshu, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-25 11:02:00"),
    BaiRong_Bairoactiontest_Totalloan("bairoactiontest_totalloan", "BY-借贷行为验证(B3410004)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_Bairoactiontest_Totalloan, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-25 11:02:00"),
    BaiRong_Bairogroup_Fraudrelation_g("bairogroup_fraudrelation_g", "BY-团伙欺诈排查(********)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_Bairogroup_Fraudrelation_g, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-25 11:02:00"),
    BaiRong_Bairong_Black_Teshu2("bairong_black_teshu2", "BY-特殊名单验证2(********)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_Bairong_Black_Teshu2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-25 11:02:00"),
    BaiRong_Bairong_Scorepdl("bairong_scorepdl", "BY-线上现金分期小额分(********)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_Bairong_Scorepdl, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-25 11:02:00"),
    BaiRong_Bairong_Scorecashon("bairong_scorecashon", "BY-线上现金分期信用分(********)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_Bairong_Scorecashon, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-25 11:02:00"),
    BaiRong_Bairong_Scoreafcashon("bairong_scoreafcashon", "BY-线上现金分期欺诈分(********)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_Bairong_Scoreafcashon, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-25 11:02:00"),
    BaiRong_Black_Jiedai2("bairong_black_jiedai2", "BY-借贷意向验证2(********)", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_Black_Jiedai2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-21 11:02:00"),
    BaiRong_PublicOpinion("bairong_PublicOpinion", "百融手机号舆情", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_PublicOpinion, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2024-04-23 23:21:00"),
    BaiRong_Scorefxsbindex_Norm("bairong_scorefxsbindex_norm", "百融风险哨兵指数-常规", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_Scorefxsbindex_Norm, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-04-23 23:21:00"),
    BaiRong_Scorefxsbindex_Qlty("bairong_scorefxsbindex_qlty", "百融风险哨兵指数-优质", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_Scorefxsbindex_Qlty, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-04-23 23:21:00"),
    BaiRong_scoreafyxbase("bairong_scoreafyxbase", "百融融御云信分-基础款", DataSourceEnum.BaiRong, "bairongFacade", CacheKeyEnum.BaiRong_scoreafyxbase, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-04-23 23:21:00"),


    TianYi_GetBasicInfo("getbasicinfo", "工商基本信息", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetBasicInfo, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-08-10 14:26:00"),
    TianYi_QiXin007("qixin007", "工商详细信息", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_QiXin007, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-07 16:16:00"),
    TianYi_GetChainRelation("getChainRelation", "企业链图查询", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetChainRelation, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-08 10:22:00"),
    TianYi_GetReportListByName("getReportListByName", "企业工商年报查询", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetReportListByName, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-08 10:57:00"),
    TianYi_LawsuitByName("lawsuitByName", "法院判决列表", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_LawsuitByName, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-08 14:13:00"),
    TianYi_GetAdminPunishByName("getAdminPunishByName", "行政处罚查询(EIV10006)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetAdminPunishByName, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-08 15:05:00"),
    TianYi_GetExecutedpersonListByName("getExecutedpersonListByName", "企业被执行信息查询(EIV10007)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetExecutedpersonListByName, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-08 15:28:00"),
    TianYi_GetRelationInfo("getRelationInfo", "企业族谱三层查询(EIV10008)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetRelationInfo, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-08 16:05:00"),
    TianYi_BeneficiaryListV2("beneficiaryListV2", "企业最终受益人查询v2(EIV10009)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_BeneficiaryListV2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-08 16:21:00"),
    TianYi_GetSeriousIllegalByName("getSeriousIllegalByName", "企业严重违法失信查询(EIV10010)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetSeriousIllegalByName, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-08 16:36:00"),
    TianYi_GetExecutionListByName("getExecutionListByName", "企业失信人列表查询(EIV10011)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetExecutionListByName, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-08 17:33:00"),
    TianYi_GetCreditGrade("getCreditGrade", "企业纳税A级查询(EIV10012)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetCreditGrade, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-09 10:01:00"),
    TianYi_GetTaxCase("getTaxCase", "重大税收违法信息查询(EIV10013)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetTaxCase, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-09 10:14:00"),
    TianYi_GetAbnormalEnterpriseByName("getAbnormalEnterpriseByName", "非正常户(EIV10014)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetAbnormalEnterpriseByName, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-09 10:27:00"),
    TianYi_GetOverDueTaxStat("getOverDueTaxStat", "企业欠税合计查询(EIV10015)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetOverDueTaxStat, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-09 10:38:00"),
    TianYi_GetChangeRecords("getChangeRecords", "工商变更记录查询(EIV10016)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetChangeRecords, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-09 10:48:00"),
    TianYi_GetMortgagesByName("getMortgagesByName", "企业动产抵押信息查询(EIV10017)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetMortgagesByName, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-09 14:28:00"),
    TianYi_GetEquityQualities("getEquityQualities", "企业股权出质信息查询(EIV10018)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetEquityQualities, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-09 14:40:00"),
    TianYi_GetOverDueTax("getOverDueTax", "企业欠税信息查询(EIV10019)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetOverDueTax, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-09-09 16:05:00"),
    TianYi_EntcommstabCta("entCommStab", "企业活跃度指数(EV001O500)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_EntcommstabCta, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-10-14 15:18:00"),
    TianYi_EntInDate("entInDate", "企业通讯在网时长(EV002O500)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_EntInDate, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-10-14 16:35:00"),
    TianYi_EntNetWorkStastus("entNetWorkStastus", "企业通讯在网状态(EV003O500)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_EntNetWorkStastus, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-10-14 16:59:00"),
    TianYi_EntCommActivity("entCommActivity", "企业稳定性指数(EV004O500)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_EntCommActivity, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-10-14 17:06:00"),
    TianYi_EntWaveTrend("entWaveTrend", "企业通讯波动指数(EV005O500)", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_EntWaveTrend, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-10-14 17:11:00"),
    TianYi_GetDeepInfo("tianyi_getDeepInfo", "天翼企业工商深度查询_ys", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetDeepInfo, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-05-30 17:11:00"),
    TianYi_EntInvestPerson("tianyi_entInvestPerson", "天翼-企业投资任职探寻", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_EntInvestPerson, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-08-29 16:40:00"),
    TianYi_GetCertificateByName("tianyi_getCertificateByName", "天翼-企业资质信息查询", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetCertificateByName, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-04-09 17:40:00"),
    TianYi_GetTrademarkByName("tianyi_getTrademarkByName", "天翼-企业商标列表查询", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetTrademarkByName, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-04-09 17:40:00"),
    TianYi_GetPatentList("tianyi_getPatentList", "天翼-专利信息列表查询", DataSourceEnum.TianYi, "tianYiFacade", CacheKeyEnum.TianYi_GetPatentList, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-04-09 17:40:00"),

    GAODE_FINANCIAL_HETU200("gaode_financial_hetu200", "生活行为风险定制(S890V801)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GaoDe_FinancialHetu200, ReportTypeStatusEnum.valid, BillingMethodEnum.feature, "2022-09-09 16:05:00"),
    GAODE_FINANCE_008_HDDZ("gaode_finance_008_hddz", "高德定制分cg06(S0008T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_008_HDDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_018CG_XYDZ("gaode_finance_018cg_xydz", "高德定制分cg07(S0018T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_018CG_XYDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_019CG_TDDZ("gaode_finance_019cg_tddz", "高德定制分cg07(S0019T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_019CG_TDDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_020CG_XYDZ("gaode_finance_020cg_xydz", "高德定制分cg07(S0020T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_020CG_XYDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_021HT_BJDZ("gaode_finance_021ht_bjdz", "高德定制分ht(S0021T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_021HT_BJDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_022HT_BJDZ("gaode_finance_022ht_bjdz", "高德定制分ht(S0022T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_022HT_BJDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_023HT_XYDZ("gaode_finance_023ht_xydz", "高德定制分ht(S0023T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_023HT_XYDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_024HT_XYDZ("gaode_finance_024ht_xydz", "高德定制分ht(S0024T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_024HT_XYDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_025HT_TDDZ("gaode_finance_025ht_tddz", "高德定制分ht(S0025T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_025HT_TDDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_026HT_TDDZ("gaode_finance_026ht_tddz", "高德定制分ht(S0026T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_026HT_TDDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_027HT_TDDZ("gaode_finance_027ht_tddz", "高德定制分ht(S0027T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_027HT_TDDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_029HT_TDDZ("gaode_finance_029ht_tddz", "高德定制分ht(S0029T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_029HT_TDDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_030HT_XYDZ("gaode_finance_030ht_xydz", "高德定制分ht(S0030T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_030HT_XYDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCIAL_06_CF("gaode_financial_06_cf", "高德标准分cg06消金分", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCIAL_06_CF, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_HT0001("gaode_finance_ht0001", "高德标准分ht总分", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_HT0001, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_HTXD("gaode_finance_htxd", "高德标准分ht小贷分", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_HTXD, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_HTXJ("gaode_finance_htxj", "高德标准分ht消金分", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_HTXJ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCE_HTYH("gaode_finance_htyh", "高德标准分ht银行分", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_HTYH, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCIAL_06_DZ("gaode_financial_06_dz", "高德定制分cg06(S0201T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCIAL_06_DZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-27 15:45:24"),
    GAODE_FINANCIAL_CHAIN("gaode_financial_chain", "高德企业经营真实性识别", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCIAL_CHAIN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-01-02 15:34:01"),
    GAODE_FINANCIAL_FEATURE("gaode_financial_feature", "高德小微特征", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCIAL_FEATURE, ReportTypeStatusEnum.valid, BillingMethodEnum.feature, "2023-01-02 15:34:01"),
    GAODE_GEO_FEATURE("gaode_geo_feature", "高德地块风险", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_GEO_FEATURE, ReportTypeStatusEnum.valid, BillingMethodEnum.feature, "2023-01-02 15:34:01"),
    GAODE_FINANCE_031HT_XYDZ("gaode_finance_031ht_xydz", "生活行为定制分(S0031T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_031HT_XYDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-20 15:34:01"),
    GAODE_FINANCE_032HT_XYDZ("gaode_finance_032ht_xydz", "生活行为定制分(S0032T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_032HT_XYDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-27 15:34:01"),
    GAODE_FINANCE_033HT_XYDZ("gaode_finance_033ht_xydz", "生活行为定制分(S0033T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_033HT_XYDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-27 15:34:02"),
    GAODE_FINANCE_015_ZDDZ("gaode_finance_015_zddz", "ZD-生活行为定制分（S0015T890）", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_015_ZDDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-07 15:34:02"),
    GAODE_FINANCE_034HT_XYDZ("gaode_finance_034ht_xydz", "高德定制分ht(S0034T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_034HT_XYDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-14 15:45:24"),
    GAODE_FINANCE_035HT_XYDZ("gaode_finance_035ht_xydz", "高德定制分ht(S0035T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_035HT_XYDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-14 15:45:25"),
    GAODE_FINANCE_07_CF("gaode_financial_07_cf", "高德标准分通用消金V7.1", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_07_CF, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-28 15:45:25"),
    GAODE_FINANCE_05_CF("gaode_finance_05_cf", "高德标准分银行分V5.0", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_05_CF, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-28 15:45:28"),
    GAODE_FINANCE_036HT_PHDZ("gaode_finance_036ht_phdz", "高德定制分ht(S0036T890)", DataSourceEnum.GaoDe, "gaoDeFacade", CacheKeyEnum.GAODE_FINANCE_036HT_PHDZ, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-04-12 15:45:25"),
    GAODE_INPUTTIPS("gaode_inputtips", "高德-输入提示", DataSourceEnum.GaoDe, "gaoDeFacadeV2", CacheKeyEnum.GAODE_INPUTTIPS, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2024-01-11 19:45:25"),
    GAODE_ADDRESS_GEOPRO("gaode_address_geoPro", "高德-地址补全", DataSourceEnum.GaoDe, "gaoDeFacadeV2", CacheKeyEnum.GAODE_ADDRESS_GEOPRO, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2024-01-11 19:45:25"),
    GAODE_ADDRESS_SPLIT("gaode_address_split", "高德-地址切分", DataSourceEnum.GaoDe, "gaoDeFacadeV2", CacheKeyEnum.GAODE_ADDRESS_SPLIT, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2024-01-24 17:45:25"),

    DianWei_BadInfo("badInfo", "不良行为核验(EBIV0001)", DataSourceEnum.DianWei, "dianWeiFacade", CacheKeyEnum.DianWei_BadInfo, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-10-17 14:27:00"),
    DianWei_SheSu("dianwei_shesu", "点微-自然人涉诉模型核查简版", DataSourceEnum.DianWei, "dianWeiFacade", CacheKeyEnum.DianWei_SheSu, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2023-07-13 18:27:00"),
    DianWei_SheSu_BankCard("dianwei_shesu_bankCard", "点微-反赌反诈", DataSourceEnum.DianWei, "dianWeiFacade", CacheKeyEnum.DianWei_SheSu_BankCard, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2023-07-13 18:27:00"),

    DianWei_badinfo_cred("dianwei_badinfo_cred", "点微-可信度行为核验", DataSourceEnum.DianWei, "dianWeiFacade", CacheKeyEnum.DianWei_badinfo_cred, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2023-08-13 10:05:00"),

    YouMeng_Olcash2("youmeng_olcash2", "品钛-友盟定制分(olcash2.0)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.YouMeng_Olcash2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-10-17 14:27:00"),
    YouMeng_Score_001_Hddz("score_001_hddz", "华道-互联网行为子分(S0001S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.YouMeng_Score_001_Hddz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-28 14:30:00"),
    Youmeng_Generalcore("youmeng_generalcore", "大额分(S001S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Generalcore, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:30:00"),
    Youmeng_Score_xiaoe("youmeng_score_xiaoe", "小额分(S002S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_xiaoe, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:31:00"),
    Youmeng_Bankscore("youmeng_bankscore", "银行标准分(S9680003)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Bankscore, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:32:00"),
    Youmeng_Score_Xiaowei("youmeng_score_xiaowei", "小微分(S9680004)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_Xiaowei, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:33:00"),
    Youmeng_YMDz02_ty("YMDz02_ty", "互联网行为子分002", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_YMDz02_ty, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:34:00"),
    Youmeng_Score_003_xydz("youmeng_score_003_xydz", "互联网行为子分(S0003S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_003_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:35:00"),
    Youmeng_Score_005_xydz("youmeng_score_005_xydz", "XYS-互联网行为子分(S0005S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_005_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:36:00"),
    Youmeng_Score_006_xydz("youmeng_score_006_xydz", "XYS-互联网行为子分(S0006S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_006_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:37:00"),
    Youmeng_Score_007_xydz("youmeng_score_007_xydz", "XYS-互联网行为子分(S0007S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_007_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:38:00"),
    Youmeng_Score_008_xydz("youmeng_score_008_xydz", "XYS-互联网行为子分(S0008S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_008_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:39:00"),
    Youmeng_Score_009_xydz("youmeng_score_009_xydz", "互联网行为子分(S0009S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_009_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:40:00"),
    Youmeng_Score_010_xydz("youmeng_score_010_xydz", "互联网行为子分(S0010S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_010_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:41:00"),
    Youmeng_Score_011_xydz("youmeng_score_011_xydz", "互联网行为子分(S0011S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_011_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:42:00"),
    Youmeng_Score_012_xydz("youmeng_score_012_xydz", "互联网行为子分(S0012S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_012_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:43:00"),
    Youmeng_Score_013_xydz("youmeng_score_013_xydz", "互联网行为子分(S0013S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_013_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:44:00"),
    Youmeng_Score_014_xydz("youmeng_score_014_xydz", "互联网行为子分(S0014S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_014_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:45:00"),
    Youmeng_Score_015_xydz("youmeng_score_015_xydz", "互联网行为子分(S0015S968)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_015_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-10 14:46:00"),
    Youmeng_YMDz01_de("YMDz01_de", "互联网行为子分001", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_YMDz01_de, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-07 14:46:00"),
    Youmeng_YMDz03_xe("YMDz03_xe", "互联网行为子分003", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_YMDz03_xe, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-07 14:46:00"),
    Youmeng_Score_016_xydz("youmeng_score_016_xydz", "友盟定制分016(XYS)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_016_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-04-06 14:46:00"),
    Youmeng_Score_017_xydz("youmeng_score_017_xydz", "友盟定制分017(XYS)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Score_017_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-04-25 14:46:00"),

    Youmeng_anticomplaintscore_001("youmeng_anticomplaintscore_001", "友盟-防营销投诉分", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_anticomplaintscore_001, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-11-15 19:48:00"),
    Youmeng_anticomplaintscore_002("youmeng_anticomplaintscore_002", "友盟-预防金融投诉分", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_anticomplaintscore_002, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-11-15 19:48:00"),

    Youmeng_marketscore_001_tddz("youmeng_marketscore_001_tddz", "友盟营销定制分001(td)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Marketscore_001_tddz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-07-05 14:46:00"),
    Youmeng_marketscore_002_hddz("youmeng_marketscore_002_hddz", "友盟营销定制分002(hd)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Marketscore_002_hddz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-07-15 17:46:00"),

    Youmeng_marketscore_002_xydz("youmeng_marketscore_002_xydz", "友盟营销定制分002(xy)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_marketscore_002_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-08-25 17:19:00"),

    Youmeng_marketscore_003_xydz("youmeng_marketscore_003_xydz", "友盟营销定制分003(xy)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Marketscore_003_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-07-15 17:46:00"),
    Youmeng_marketscore_004_xydz("youmeng_marketscore_004_xydz", "友盟营销定制分004(xy)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Marketscore_004_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-07-15 17:46:00"),

    Youmeng_marketscore_004_xydz_trans("youmeng_marketscore_004_xydz_trans", "友盟营销定制分004(xy)-接口转换", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Marketscore_004_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-08-29 14:45:00"),
    Youmeng_marketscore_004_xydz_trans2_S0015S968("youmeng_marketscore_004_xydz_trans2_S0015S968", "友盟营销定制分004(xy)-接口转换2-S0015S968", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Marketscore_004_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-09-01 17:36:00"),
    Youmeng_marketscore_003_xydz_trans("youmeng_marketscore_003_xydz_trans", "友盟营销定制分003(xy)-接口转换", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_Marketscore_003_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-07-24 18:46:00"),
    Youmeng_marketscore_standard("youmeng_marketscore_standard", "友盟-借贷意愿分", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_marketscore_standard, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-11-22 09:54:00"),

    Youmeng_marketscore_006_hddz("youmeng_marketscore_006_hddz", "友盟营销定制分006(hd)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_marketscore_006_hddz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-04-01 17:00:00"),
    Youmeng_marketscore_005_ht("youmeng_marketscore_005_ht", "友盟营销定制分005(ht)", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_marketscore_005_ht, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-05-22 14:40:00"),
    Youmeng_marketscore_marketing_xiaodai("youmeng_marketscore_marketing_xiaodai", "友盟-营销资质分_小贷场景", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_marketscore_marketing_xiaodai, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-06-13 17:40:00"),
    Youmeng_marketscore_0010_dz("youmeng_marketscore_0010_dz", "友盟营销定制分0010", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_marketscore_0010_dz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-05-28 23:00:22"),

    Youmeng_professional_profile_01_taxi_driver("youmeng_professional_profile_01_taxi_driver", "友盟-预测出租车或网约车司机指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_01_taxi_driver, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-16 00:11:13"),
    Youmeng_professional_profile_02_college_student("youmeng_professional_profile_02_college_student", "友盟-预测大学生指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_02_college_student, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-16 00:11:13"),
    Youmeng_professional_profile_03_accountant("youmeng_professional_profile_03_accountant", "友盟-预测会计指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_03_accountant, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-16 00:11:13"),
    Youmeng_professional_profile_04_truck_driver("youmeng_professional_profile_04_truck_driver", "友盟-预测货车司机指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_04_truck_driver, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-16 00:11:13"),
    Youmeng_professional_profile_05_side_job("youmeng_professional_profile_05_side_job", "友盟-预测兼职指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_05_side_job, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-16 00:11:13"),
    Youmeng_professional_profile_06_worker("youmeng_professional_profile_06_worker", "友盟-预测建筑工程人员指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_06_worker, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-16 00:11:13"),
    Youmeng_professional_profile_07_teacher("youmeng_professional_profile_07_teacher", "友盟-预测教师指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_07_teacher, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-16 00:11:13"),
    Youmeng_professional_profile_08_delivery_driver("youmeng_professional_profile_08_delivery_driver", "友盟-预测快递员指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_08_delivery_driver, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-16 00:11:13"),
    Youmeng_professional_profile_09_delivery_biker("youmeng_professional_profile_09_delivery_biker", "友盟-预测外卖骑手指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_09_delivery_biker, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-16 00:11:13"),
    Youmeng_professional_profile_10_doctor("youmeng_professional_profile_10_doctor", "友盟-预测医生指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_10_doctor, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-16 00:11:13"),
    Youmeng_professional_profile_11_nurse("youmeng_professional_profile_11_nurse", "友盟-预测护士指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_11_nurse, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-02 22:53:48"),
    Youmeng_professional_profile_12_HR("youmeng_professional_profile_12_HR", "友盟-预测人事指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_12_HR, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-02 22:53:48"),
    Youmeng_professional_profile_13_wholesaler("youmeng_professional_profile_13_wholesaler", "友盟-预测批发商指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_13_wholesaler, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-02 22:53:48"),
    Youmeng_professional_profile_14_lawyer("youmeng_professional_profile_14_lawyer", "友盟-预测律师指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_14_lawyer, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-02 22:53:48"),
    Youmeng_professional_profile_15_housekeeper("youmeng_professional_profile_15_housekeeper", "友盟-预测家政人员指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_15_housekeeper, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-02 22:53:48"),
    Youmeng_professional_profile_16_cargo_owner("youmeng_professional_profile_16_cargo_owner", "友盟-预测货主指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_16_cargo_owner, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-02 22:53:48"),
    Youmeng_professional_profile_17_power_leveling("youmeng_professional_profile_17_power_leveling", "友盟-预测代练指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_17_power_leveling, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-02 22:53:48"),
    Youmeng_professional_profile_18_developer("youmeng_professional_profile_18_developer", "友盟-预测程序员指数A", DataSourceEnum.YouMeng, "youMengFacade", CacheKeyEnum.Youmeng_professional_profile_18_developer, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-02 22:53:48"),
    Youmeng_professional_profile("youmeng_professional_profile", "友盟职业预测指数", DataSourceEnum.YouMeng, "youMengFacade", null, ReportTypeStatusEnum.valid, null, "2025-07-11 11:32:41"),

    Tencent_Cloud_blq_Olcash2("cloud_blq_olcash2", "品钛-腾讯安全定制分(olcash2.0)", DataSourceEnum.Tencent, "tencentFacade", CacheKeyEnum.Tencent_Cloud_blq_Olcash2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-10-17 14:27:00"),
    Tencent_Cloud_Security_001_Hddz("cloud_security_001_hddz", "华道-负面行为子分(S0001C556)", DataSourceEnum.Tencent, "tencentFacade", CacheKeyEnum.Tencent_Cloud_Security_001_Hddz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-25 15:07:00"),
    Tencent_Tengxun_Lianbang_2_xydz("tengxun_lianbang_2_xydz", "XYS-负面行为子分(S0002C556)", DataSourceEnum.Tencent, "tencentFacade", CacheKeyEnum.Tencent_Tengxun_Lianbang_2_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-01-31 15:00:02"),
    Tencent_Tengxun_Lianbang_3_xydz("tengxun_lianbang_3_xydz", "负面行为子分(S0003C556)", DataSourceEnum.Tencent, "tencentFacade", CacheKeyEnum.Tencent_Tengxun_Lianbang_3_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-01-31 15:00:03"),
    Tencent_tcloud_blg_dzscore001("tcloud_blg_dzscore001", "电话邦定制分001(ScoreDHBDz_001)", DataSourceEnum.Tencent, "tencentFacade", CacheKeyEnum.Tencent_tcloud_blg_dzscore001, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-07 15:07:00"),
    Tencent_cloud_security_general_v4("cloud_security_general_v4", "负面行为子分(S1010102)", DataSourceEnum.Tencent, "tencentFacade", CacheKeyEnum.Tencent_cloud_security_general_v4, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-07 15:07:01"),

    Tencent_Antifraud_Fenceng("antifraud_fenceng", "负面通用风险分层分(S3090002)", DataSourceEnum.Tencent, "tencentApiFacade", CacheKeyEnum.Tencent_Antifraud_Fenceng, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-27 20:38:00"),
    Tencent_Antifraud_HangyeV1_4("antifraud_hangyev1_4", "负面行业风险评估(S3090004)", DataSourceEnum.Tencent, "tencentApiFacade", CacheKeyEnum.Tencent_Antifraud_HangyeV1_4, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-27 22:38:00"),
    Tencent_Antifraud_82("antifraud_82", "负面反欺诈标准评分(S3090006)", DataSourceEnum.Tencent, "tencentApiFacade", CacheKeyEnum.Tencent_Antifraud_82, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-27 23:21:00"),
    Tencent_Antifraud_v6high("antifraud_v6high", "负面反欺诈标准评分6(S3090007)", DataSourceEnum.Tencent, "tencentApiFacade", CacheKeyEnum.Tencent_Antifraud_v6high, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-20 10:27:00"),
    Tencent_Antifraud_v6low("txqcloud_antifraud_v6low", "负面反欺诈标准评分L6(S3090012)", DataSourceEnum.Tencent, "tencentApiFacade", CacheKeyEnum.Tencent_Antifraud_v6low, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-21 10:27:00"),
    Tencent_Antifraud_v6Tongyong("txqcloud_antifraud_v6tongyong", "负面反欺诈标准评分T6(S3090013)", DataSourceEnum.Tencent, "tencentApiFacade", CacheKeyEnum.Tencent_Antifraud_v6Tongyong, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-21 10:27:10"),

    Tencent_Antifraud_v6Tongyong_trans("txqcloud_antifraud_v6tongyong_trans", "负面反欺诈标准评分T6(S3090013)-接口转换", DataSourceEnum.Tencent, "tencentApiFacade", CacheKeyEnum.Tencent_Antifraud_v6Tongyong, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-07-24 17:28:00"),
    Tencent_Txqcloud_Antifraud("txqcloud_antifraud", "负面通用反欺诈评分(S3090001)", DataSourceEnum.Tencent, "tencentApiFacade", CacheKeyEnum.Tencent_Txqcloud_Antifraud, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-01-31 15:00:00"),
    Tencent_Txqcloud_Antifraud_Daizhongv2_1("txqcloud_antifraud_daizhongv2_1", "负面贷中行为评分(S3090005)", DataSourceEnum.Tencent, "tencentApiFacade", CacheKeyEnum.Tencent_Txqcloud_Antifraud_Daizhongv2_1, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-01-31 15:00:01"),
    Tencent_Txqcloud_MultipleLoans("txqcloud_MultipleLoans", "腾讯反电诈", DataSourceEnum.Tencent, "tencentApiFacade", CacheKeyEnum.Tencent_Txqcloud_MultipleLoans, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2023-05-10 15:00:01"),

    HaoDuoShu_Consume_Info_V4("hdshu_consume_info_v4", "好多数消费信息v4", DataSourceEnum.HaoDuoShu, "haoDuoShuFacade", CacheKeyEnum.HaoDuoShu_Consume_Info_V4, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-10-17 14:27:00"),

    ZhongZhiCheng_HaoRi("zzcheng_haori_flow_monitor", "中智诚昊日流量监控", DataSourceEnum.ZhongZhiCheng, "zhongZhiChengFacade", CacheKeyEnum.ZhongZhiCheng_HaoRi, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-10-17 14:27:00"),
    Zzcheng_blacklistV3("zzcheng_blacklistV3", "中智诚黑名单V3高阶版", DataSourceEnum.ZhongZhiCheng, "zhongZhiChengFacade", CacheKeyEnum.zzcheng_blacklistV3, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-23 16:27:49"),

    Haohan_txfenceng("haohan_txfenceng", "负面通用风险分层分(S3090003)", DataSourceEnum.Haohan, "haohanFacade", CacheKeyEnum.Haohan_txfenceng, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-24 15:27:00"),
    Haohan_antifraud_hangyeV1_4("haohan_antifraud_hangyeV1_4", "负面行业风险评估(S3090008)", DataSourceEnum.Haohan, "haohanFacade", CacheKeyEnum.Haohan_antifraud_hangyeV1_4, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-24 15:28:00"),
    Haohan_antifraud__82("haohan_antifraud__82", "负面反欺诈标准评分(S3090010)", DataSourceEnum.Haohan, "haohanFacade", CacheKeyEnum.Haohan_antifraud__82, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-24 15:29:00"),
    Haohan_antifraud_v6high("haohan_antifraud_v6high", "负面反欺诈标准评分6(S3090011)", DataSourceEnum.Haohan, "haohanFacade", CacheKeyEnum.Haohan_antifraud_v6high, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-30 10:29:00"),
    Haohan_antifraud_v6low("haohan_antifraud_v6low", "负面反欺诈标准评分L6(S3090014)", DataSourceEnum.Haohan, "haohanFacade", CacheKeyEnum.Haohan_antifraud_v6low, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-2-21 10:29:00"),
    Haohan_antifraud_v6tongyong("haohan_antifraud_v6tongyong", "负面反欺诈标准评分T6(S3090015)", DataSourceEnum.Haohan, "haohanFacade", CacheKeyEnum.Haohan_antifraud_v6tongyong, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-2-21 10:29:01"),
    Haohan_txfenceng_2("haohan_txfenceng_2", "负面通用风险分层分(S3090017)", DataSourceEnum.Haohan, "haohanFacade", CacheKeyEnum.Haohan_txfenceng_2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-05-11 15:27:00"),

    Mayi_ec_afv_dy("mayi_ec_afv_dy", "反欺诈标准分(S0001E389)", DataSourceEnum.Yidun, "yidunFacade", CacheKeyEnum.Mayi_ec_afv_dy, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-11-24 15:30:00"),

    Yinlian_merchantv2("yinnlianzhihui_merchantv2", "小微企业收入画像(EIV1S003)", DataSourceEnum.Yinlian, "yinlianFacade", CacheKeyEnum.Yinlian_merchantv2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2022-12-29 15:30:00"),
    Yinlian_merchantv2_1("yinnlianzhihui_merchantv2_1", "经营收入画像(ES0101001)", DataSourceEnum.Yinlian, "yinlianFacade", CacheKeyEnum.Yinlian_merchantv2_1, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-20 14:30:00"),

    fulin_dongjiangao("fulin_dongjiangao", "孚临董监高", DataSourceEnum.Fulin, "fulinFacade", CacheKeyEnum.fulin_dongjiangao, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-01-11 11:12:57"),

    Haina_Score_Chengyi("haina_score_chengyi", "HN-橙意分(S0004P327)", DataSourceEnum.Haina, "hainaFacade", CacheKeyEnum.Haina_Score_Chengyi, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-03 14:00:57"),
    Haina_Score_Haibao("haina_score_haibao", "HN-海豹分(M3270001)", DataSourceEnum.Haina, "hainaFacade", CacheKeyEnum.Haina_Score_Haibao, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-03 14:01:57"),
    Haina_Score_Hujing("haina_score_hujing", "HN-虎鲸分(S3270002)", DataSourceEnum.Haina, "hainaFacade", CacheKeyEnum.Haina_Score_Hujing, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-03 14:02:57"),
    Haina_Score_Moyu("haina_score_moyu", "HN-墨鱼分(B3270003)", DataSourceEnum.Haina, "hainaFacade", CacheKeyEnum.Haina_Score_Moyu, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-03 14:03:57"),
    Haina_HNFQLHA("haina_HNFQLHA", "海纳蚁盾模型分", DataSourceEnum.Haina, "hainaFacadeV2", CacheKeyEnum.Haina_HNFQLHA, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-05-11 20:10:02"),
    Haina_HNZSQLHDC("haina_HNZSQLHDC", "海纳指数", DataSourceEnum.Haina, "hainaFacadeV2", CacheKeyEnum.Haina_HNZSQLHDC, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-11-05 13:34:37"),
    Haina_HNZSQLHHB("haina_HNZSQLHHB", "海纳指数HB", DataSourceEnum.Haina, "hainaFacadeV2", CacheKeyEnum.Haina_HNZSQLHHB, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-12-27 12:58:29"),
    Haina_HNZSQLHPH("haina_HNZSQLHPH", "海纳指数QLHPH", DataSourceEnum.Haina, "hainaFacadeV2", CacheKeyEnum.Haina_HNZSQLHPH, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-01-11 17:39:46"),

    Baiqishi_Black_Tongji("baiqishi_black_tongji", "BQS-调用统计(B3500001)", DataSourceEnum.Baiqishi, "baiqishiFacade", CacheKeyEnum.Baiqishi_Black_Tongji, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-07 10:00:00"),
    Baiqishi_Black_Guanxiweb("baiqishi_black_guanxiweb", "BQS-关系网络(B3500002)", DataSourceEnum.Baiqishi, "baiqishiFacade", CacheKeyEnum.Baiqishi_Black_Guanxiweb, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-07 10:00:01"),
    Baiqishi_Score_Qishih1("baiqishi_score_qishih1", "BQS-骑士H1分(M3500003)", DataSourceEnum.Baiqishi, "baiqishiFacade", CacheKeyEnum.Baiqishi_Score_Qishih1, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-07 10:00:02"),

    Xinyisou_Score_Mingmou2("xinyisou_score_mingmou2", "XYS-明眸分2（S3260002）", DataSourceEnum.Xinyisou, "xinyisouFacade", CacheKeyEnum.Xinyisou_Score_Mingmou2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-07 10:00:57"),
    Xinyisou_Score_Mingjian2("xinyisou_score_mingjian2", "XYS-明鉴分2（E3260003）", DataSourceEnum.Xinyisou, "xinyisouFacade", CacheKeyEnum.Xinyisou_Score_Mingjian2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-07 10:01:57"),
    Xinyisou_Score_Biyu2("xinyisou_score_biyu2", "XYS-碧玉分2（SP3260004）", DataSourceEnum.Xinyisou, "xinyisouFacade", CacheKeyEnum.Xinyisou_Score_Biyu2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-07 10:02:57"),
    Xinyisou_black("xinyisou_black", "XYS-黑名单（B3260001）", DataSourceEnum.Xinyisou, "xinyisouFacade", CacheKeyEnum.Xinyisou_black, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2023-03-07 10:02:57"),
    Xinyisou_three_elements_check("xinyisou_three_elements_check", "信易搜三要素核验", DataSourceEnum.Xinyisou, "xinyisouFacade", CacheKeyEnum.Xinyisou_three_elements_check, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-19 19:00:00"),

    Fahai_v2_query("fahai_v2_query", "企业涉诉列表查询(EIV10024)", DataSourceEnum.Fahai, "fahaiFacade", CacheKeyEnum.Fahai_v2_query, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2023-02-06 17:43:23"),
    Fahai_v2_person("fahai_v2_person", "关联人涉诉列表查询(EIV10025)", DataSourceEnum.Fahai, "fahaiFacade", CacheKeyEnum.Fahai_v2_person, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2023-02-06 17:43:23"),
    Fahai_v2_export("fahai_v2_export", "涉诉详情查询(EIV10026)", DataSourceEnum.Fahai, "fahaiFacade", CacheKeyEnum.Fahai_v2_export, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2023-02-06 17:43:23"),

    Zunxin_score_dingzhi("zunxin_score_dingzhi", "ZX-反欺诈定制分(SCP3280001)", DataSourceEnum.Zunxin, "zunxinFacade", CacheKeyEnum.Zunxin_score_dingzhi, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-07 16:51:57"),
    Zunxin_score_tongrui("zunxin_score_tongrui", "ZX-瞳睿分(CP3280002)", DataSourceEnum.Zunxin, "zunxinFacade", CacheKeyEnum.Zunxin_score_tongrui, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-07 16:51:57"),
    Zunxin_score_changhuan("zunxin_score_changhuan", "ZX-偿还能力分(P3280003)", DataSourceEnum.Zunxin, "zunxinFacade", CacheKeyEnum.Zunxin_score_changhuan, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-07 16:51:57"),
    Zunxin_risklevel_daiqian("zunxin_risklevel_daiqian", "ZX-贷前预警(S3280004)", DataSourceEnum.Zunxin, "zunxinFacade", CacheKeyEnum.Zunxin_risklevel_daiqian, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-07 16:51:57"),

    Liantong_gdcBycarCount("liantong_gdcBycarCount", "GDC列车乘车总次数核验(O3680001)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_gdcBycarCount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:00:00"),
    Liantong_gdcConsumptionAmountCount("liantong_gdcConsumptionAmountCount", "GDC列车车费消费总金额核验(O3680002)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_gdcConsumptionAmountCount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:00:01"),
    Liantong_consumptionTotalAmount("liantong_consumptionTotalAmount", "铁路乘车人车费消费总金额核验(O3680003)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_consumptionTotalAmount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:00:02"),
    Liantong_rideCount("liantong_rideCount", "铁路用户乘车总次数核验(O3680004)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_rideCount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:00:03"),
    Liantong_ptRideCount("liantong_ptRideCount", "铁路用户普通列车乘车总次数核验(O3680005)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_ptRideCount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:00:04"),
    Liantong_totalMileage("liantong_totalMileage", "乘车总里程核验(O3680006)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_totalMileage, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:00:05"),
    Liantong_totalTrips24Count("liantong_totalTrips24Count", "乘车总次数24月内核验(O3680007)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_totalTrips24Count, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:00:06"),
    Liantong_weekDaysTravelProportion("liantong_weekDaysTravelProportion", "工作日乘车比例核验(O3680008)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_weekDaysTravelProportion, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:00:07"),
    Liantong_ptTotalConsumptionAmount("liantong_ptTotalConsumptionAmount", "普通列车车费消费总金额核验(O3680009)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_ptTotalConsumptionAmount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:00:08"),
    Liantong_seniorRideCount("liantong_seniorRideCount", "高端席别乘车次数核验(O3680010)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_seniorRideCount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:00:09"),
    Liantong_ordinarySeatsCount("liantong_ordinarySeatsCount", "普通席别乘车数量(其他)(O3680011)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_ordinarySeatsCount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:01:00"),
    Liantong_threemonthbycarcount("liantong_threemonthbycarcount", "乘车人近3月乘车总次数核验(O3680015)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_threemonthbycarcount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:02:00"),
    Liantong_phonebuyticketpercentage("liantong_phonebuyticketpercentage", "乘车人手机购票比例核验(O3680016)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_phonebuyticketpercentage, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:03:00"),
    Liantong_ahead24buyticketpercentage("liantong_ahead24buyticketpercentage", "乘车人提前0-24小时购票比例核验(O3680017)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_ahead24buyticketpercentage, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:04:00"),
    Liantong_buytickettotalcount("liantong_buytickettotalcount", "乘车人购票总次数核验(O3680018)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_buytickettotalcount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:05:00"),
    Liantong_refundticketcount("liantong_refundticketcount", "购票人的退票次数核验(O3680019)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_refundticketcount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:06:00"),
    Liantong_relevanceidcardcount("liantong_relevanceidcardcount", "用户关联身份证个数核验(O3680020)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_relevanceidcardcount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:07:00"),
    Liantong_recentThreeMonthAverageCall("liantong_recentThreeMonthAverageCall", "近三个月话费均值查询(O3680012)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_recentThreeMonthAverageCall, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:08:00"),
    Liantong_recentThreeMonthOutagesNum("liantong_recentThreeMonthOutagesNum", "近三个月欠费停机次数(O3680013)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_recentThreeMonthOutagesNum, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-10 16:09:00"),
    Liantong_twocardcompared("liantong_twocardcompared", "二次卡比对(O3680014)", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_twocardcompared, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-02-01 16:10:00"),
    Liantong_hmwdScore("liantong_hmwdScore", "联通_号码稳定性", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_hmwdScore, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-03-21 15:57:31"),
    Liantong_gdcBuyingTicketsCount("liantong_gdcBuyingTicketsCount", "联通_乘车人GDC列车购票总次数核验", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_gdcBuyingTicketsCount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),
    Liantong_consumptionAmountAverage("liantong_consumptionAmountAverage", "联通_用户车费消费平均金额核验", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_consumptionAmountAverage, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),
    Liantong_ptBuyingTicketsCount("liantong_ptBuyingTicketsCount", "联通_普通列车购票总次数核验", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_ptBuyingTicketsCount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),
    Liantong_totalTravelTime("liantong_totalTravelTime", "联通_总旅行时长核验", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_totalTravelTime, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),
    Liantong_gdcRideratio("liantong_gdcRideratio", "联通_GDC等级列车乘车比例核验", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_gdcRideratio, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),
    Liantong_offlineTicketPurchaseProportion("liantong_offlineTicketPurchaseProportion", "联通_线下购票比例核验", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_offlineTicketPurchaseProportion, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),
    Liantong_buyTicketsInAdvanceProportion24("liantong_buyTicketsInAdvanceProportion24", "联通_提前N小时购票比例（N=24-48）核验", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_buyTicketsInAdvanceProportion24, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),
    Liantong_buyTicketsInAdvanceProportion48("liantong_buyTicketsInAdvanceProportion48", "联通_提前N小时购票比例（N=48以上）核验", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_buyTicketsInAdvanceProportion48, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),
    Liantong_buyRidingInsuranceCount("liantong_buyRidingInsuranceCount", "联通_购乘意险数量", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_buyRidingInsuranceCount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),
    Liantong_rateOfBuyRidingInsuranceCount("liantong_rateOfBuyRidingInsuranceCount", "联通_购乘意险数量占购票张数的比例", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_rateOfBuyRidingInsuranceCount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),
    Liantong_fareConsumePerKilometreAverageAmount("liantong_fareConsumePerKilometreAverageAmount", "联通_车费消费每公里平均金额", DataSourceEnum.Liantong, "liantongFacade", CacheKeyEnum.Liantong_fareConsumePerKilometreAverageAmount, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 10:26:30"),

    Blacklist_hbase_black_detail("hbase_black_detail", "本地黑名单", DataSourceEnum.Blacklist, "blacklistFacade", CacheKeyEnum.Blacklist_hbase_black_detail, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2023-02-21 15:24:52"),
    Blacklist_hbase_antifraud_blacklist("hbase_antifraud_blacklist", "本地反欺诈名单", DataSourceEnum.Blacklist, "blacklistFacade", CacheKeyEnum.Blacklist_hbase_antifraud_blacklist, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2023-02-21 15:24:52"),

    SHUIDI_SIYAOSUHEYAN("shuidi_siyaosuheyan", "工商四要素验证(EBV16900)", DataSourceEnum.Shuidi, "shuidiFacade", CacheKeyEnum.SHUIDI_SIYAOSUHEYAN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-08 16:10:00"),

    YIMING_SIYAOSUHEYAN("yiming_siyaosuheyan", "一鸣工商四要素核验", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_SIYAOSUHEYAN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-14 16:10:00"),

    YIMING_SIYAOSUHEYAN_TOZX("yiming_siyaosuheyan_tozx", "一鸣工商四要素核验-接口转换", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_SIYAOSUHEYAN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-08-29 17:31:00"),
    YIMING_MOBILE3SE("yiming_Mobile3SE", "一鸣运营商三要素验证简版", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3SE, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-27 16:10:00"),
    YIMING_MOBILE3DD("yiming_Mobile3DD", "一鸣运营商三要素验证详版", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3DD, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-27 16:11:00"),
    YIMING_MOBILE3INNET("yiming_Mobile3innet", "一鸣运营商手机号时长", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3INNET, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-27 16:12:00"),
    YIMING_MOBILE3INNET_NEW("yiming_Mobile3innet_new", "一鸣运营商手机号时长(新)", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3INNET, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-19 21:12:00"),
    YIMING_MOBILE3STATE("yiming_Mobile3state", "一鸣运营商手机号状态", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3STATE, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-27 16:13:00"),
    YIMING_MOBILE2NDSELL("yiming_Mobile2ndSell", "一鸣运营商商二次卡验证", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE2NDSELL, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-27 16:14:00"),
    YIMING_CHINAMOBILE_INNET("yiming_ChinaMobile_innet", "一鸣移动单网在网时长", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_CHINAMOBILE_INNET, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-05-11 16:14:00"),
    YIMING_MOBILE3INNET_SHA256("yiming_Mobile3innet_sha256", "一鸣运营商手机号时长_sha256", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3INNET_SHA256, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-05-22 16:14:00"),
    YIMING_MOBILE3INNET_SHA256_NEW("yiming_Mobile3innet_sha256_new", "一鸣运营商手机号时长_sha256(新)", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3INNET_SHA256, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-19 21:14:00"),
    YIMING_MOBILE3SE_MINGWEN("yiming_Mobile3SE_mingwen", "一鸣运营商三要素验证简版_明文", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3SE_MINGWEN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:10:01"),
    YIMING_MOBILE3SE_SHA256("yiming_Mobile3SE_sha256", "一鸣运营商三要素验证简版_sha256", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3SE_SHA256, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:10:02"),
    YIMING_MOBILE3DD_MINGWEN("yiming_Mobile3DD_mingwen", "一鸣运营商三要素验证详版_明文", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3DD_MINGWEN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:10:03"),
    YIMING_MOBILE3DD_SHA256("yiming_Mobile3DD_sha256", "一鸣运营商三要素验证详版_sha256", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3DD_SHA256, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:10:04"),
    YIMING_MOBILE3INNET_MINGWEN("yiming_Mobile3innet_mingwen", "一鸣运营商手机号时长_明文", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3INNET_MINGWEN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:11:00"),
    YIMING_MOBILE3STATE_MINGWEN("yiming_Mobile3state_mingwen", "一鸣运营商手机号状态_明文", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3STATE_MINGWEN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:12:00"),
    YIMING_MOBILE3STATE_SHA256("yiming_Mobile3state_sha256", "一鸣运营商手机号状态_sha256", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE3STATE_SHA256, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:13:00"),
//    YIMING_MOBILE2NDSELL_MINGWEN("yiming_Mobile2ndSell_mingwen", "一鸣运营商二次卡验证_明文", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE2NDSELL_MINGWEN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:14:00"),
//    YIMING_MOBILE2NDSELL_SHA256("yiming_Mobile2ndSell_sha256", "一鸣运营商二次卡验证_sha256", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE2NDSELL_SHA256, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:15:00"),
    YIMING_MOBILE2ELEMANDN_MINGWEN("yiming_Mobile2EleMandN_mingwen", "一鸣运营商二要素手机号姓名验证_明文", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE2ELEMANDN_MINGWEN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:16:00"),
    YIMING_MOBILE2ELEMANDN_MD5("yiming_Mobile2EleMandN_md5", "一鸣运营商二要素手机号姓名验证_md5", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE2ELEMANDN_MD5, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:17:00"),
    YIMING_MOBILE2ELEMANDN_SHA256("yiming_Mobile2EleMandN_sha256", "一鸣运营商二要素手机号姓名验证_sha256", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE2ELEMANDN_SHA256, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:18:00"),
    YIMING_MOBILE2ELEMANDIDV2_MINGWEN("yiming_Mobile2EleMandIdV2_mingwen", "一鸣运营商二要素手机号证件号验证_明文", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE2ELEMANDIDV2_MINGWEN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:19:00"),
    YIMING_MOBILE2ELEMANDIDV2_MD5("yiming_Mobile2EleMandIdV2_md5", "一鸣运营商二要素手机号证件号验证_md5", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE2ELEMANDIDV2_MD5, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:20:00"),
    YIMING_MOBILE2ELEMANDIDV2_SHA256("yiming_Mobile2EleMandIdV2_sha256", "一鸣运营商二要素手机号证件号验证_sha256", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_MOBILE2ELEMANDIDV2_SHA256, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-26 10:30:00"),

    YIMING_TRANSFERFEE("yiming_TransferFee", "一鸣携号转网验证", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_TRANSFERFEE, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-11-07 17:45:00"),

    YIMING_IDNCHECK_MINGWEN("yiming_idNCheck_mingwen", "一鸣二要素核验姓名身份证", DataSourceEnum.Yiming, "yimingFacade", CacheKeyEnum.YIMING_IDNCHECK_MINGWEN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-11-07 17:45:00"),


    DUXIAOMAN_GENERAL_COMPANYOWNER_V1A("duxiaoman_general_companyowner_v1a", "度小满小微企业核查", DataSourceEnum.Duxiaoman, "duxiaomanFacade", CacheKeyEnum.DUXIAOMAN_GENERAL_COMPANYOWNER_V1A, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-03-22 16:10:00"),
    DUXIAOMAN_COMPANYOWNER_QLHCREDIT_V1("duxiaoman_companyowner_qlhcredit_v1", "度小满_人企关联核验_定制版", DataSourceEnum.Duxiaoman, "duxiaomanFacadeV2", CacheKeyEnum.DUXIAOMAN_COMPANYOWNER_QLHCREDIT_V1, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-07-15 17:00:00"),

    YINRONGZHIXIN_APPLY_ANTIFRAUD_V4("yinrongzhixin_apply_antifraud_V4", "银融致信_反欺诈V4", DataSourceEnum.YinRongZhiXin, "yinRongZhiXinFacade", CacheKeyEnum.YINRONGZHIXIN_APPLY_ANTIFRAUD_V4, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-04-06 16:10:00"),
    YINRONGZHIXIN_MOBILETIME("yinrongzhixin_mobileTime", "银融致信-在网时长", DataSourceEnum.YinRongZhiXin, "yinRongZhiXinFacade", CacheKeyEnum.YINRONGZHIXIN_MOBILETIME, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-19 20:10:00"),
    YINRONGZHIXIN_CHANGECHECK("yinrongzhixin_changeCheck", "银融致信-携号转网", DataSourceEnum.YinRongZhiXin, "yinRongZhiXinFacade", CacheKeyEnum.YINRONGZHIXIN_CHANGECHECK, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-26 17:10:00"),
    YINRONGZHIXIN_THREEELECHECK("yinrongzhixin_threeEleCheck", "银融致信-三要素核验", DataSourceEnum.YinRongZhiXin, "yinRongZhiXinFacade", CacheKeyEnum.YINRONGZHIXIN_THREEELECHECK, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-06-13 17:10:00"),

    YUANSU_ENTERPRISE_DEEP("yuansu_enterprise_deep", "元素企业工商深度查询", DataSourceEnum.Yuansu, "yuansuFacade", CacheKeyEnum.YUANSU_ENTERPRISE_DEEP, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-05-11 16:10:00"),

    DAOSHUZHOU_USERONLINETIMESMD5_JITUAN("daoshuzhou_userOnlineTimesMD5_jituan", "道枢宙在网时长_md5_集团", DataSourceEnum.Daoshuzhou, "daoshuzhouFacade", CacheKeyEnum.DAOSHUZHOU_USERONLINETIMESMD5_JITUAN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-05-22 16:10:00"),
    DAOSHUZHOU_USERONLINETIMES256_JITUAN("daoshuzhou_userOnlineTimes256_jituan", "道枢宙在网时长_sha256_集团", DataSourceEnum.Daoshuzhou, "daoshuzhouFacade", CacheKeyEnum.DAOSHUZHOU_USERONLINETIMES256_JITUAN, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-05-22 16:11:00"),
    DAOSHUZHOU_USERONLINETIMESMD5_QUDAO("daoshuzhou_userOnlineTimesMD5_qudao", "道枢宙在网时长_md5_渠道", DataSourceEnum.Daoshuzhou, "daoshuzhouFacade", CacheKeyEnum.DAOSHUZHOU_USERONLINETIMESMD5_QUDAO, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-05-25 16:10:00"),
    DAOSHUZHOU_USERONLINETIMES256_QUDAO("daoshuzhou_userOnlineTimes256_qudao", "道枢宙在网时长_sha256_渠道", DataSourceEnum.Daoshuzhou, "daoshuzhouFacade", CacheKeyEnum.DAOSHUZHOU_USERONLINETIMES256_QUDAO, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-05-25 16:12:00"),

    Pudao_generalcore("pudao_generalcore", "爱信小贷-大额通用分(PD968S001)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_generalcore, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_bankscore("pudao_bankscore", "爱信小贷-银行分(PD968S002)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_bankscore, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_xiaoe("pudao_score_xiaoe", "爱信小贷-21版小额分(PD968S003)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_xiaoe, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_xiaowei("pudao_score_xiaowei", "爱信小贷-21版小微分(PD968S004)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_xiaowei, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_kaifangpingtai("pudao_kaifangpingtai", "爱信小贷-开放平台1", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_kaifangpingtai, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_olcash2("pudao_olcash2", "爱信小贷-友盟定制分(olcash2.0)(PD968S0201)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_olcash2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    PDDz01_de("PDDz01_de", "爱信小贷-积木高级版01_de(ScorePDDz_001)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.PDDz01_de, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    PDDz02_ty("PDDz02_ty", "爱信小贷-积木高级版02_ty(ScorePDDz_002)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.PDDz02_ty, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    PDDz03_xe("PDDz03_xe", "爱信小贷-积木高级版03_xe(ScorePDDz_003)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.PDDz03_xe, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_001_hddz("pudao_score_001_hddz", "爱信小贷-互联网行为子分(PD0001S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_001_hddz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_002_hndz("pudao_score_002_hndz", "爱信小贷-互联网行为子分(PD0002S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_002_hndz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_003_xydz("pudao_score_003_xydz", "爱信小贷-互联网行为子分(PD0003S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_003_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_004_xydz("pudao_score_004_xydz", "爱信小贷-互联网行为子分(PD0004S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_004_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_005_xydz("pudao_score_005_xydz", "爱信小贷-互联网行为子分(PD0005S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_005_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_006_xydz("pudao_score_006_xydz", "爱信小贷-互联网行为子分(PD0006S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_006_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_007_xydz("pudao_score_007_xydz", "爱信小贷-互联网行为子分(PD0007S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_007_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_008_xydz("pudao_score_008_xydz", "爱信小贷-互联网行为子分(PD0008S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_008_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_009_xydz("pudao_score_009_xydz", "爱信小贷-互联网行为分(PD0009S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_009_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_010_xydz("pudao_score_010_xydz", "爱信小贷-互联网行为子分(PD0010S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_010_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_011_tddz("pudao_score_011_tddz", "爱信小贷-互联网行为子分(PD0011S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_011_tddz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_012_xydz("pudao_score_012_xydz", "爱信小贷-互联网行为子分(PD0012S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_012_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_013_xydz("pudao_score_013_xydz", "爱信小贷-互联网行为子分(PD0013S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_013_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_014_xydz("pudao_score_014_xydz", "爱信小贷-互联网行为子分(PD0014S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_014_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_015_xydz("pudao_score_015_xydz", "爱信小贷-互联网行为子分(PD0015S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_015_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_016_xydz("pudao_score_016_xydz", "爱信小贷-互联网行为子分(PD0016S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_016_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),
    Pudao_score_017_xydz("pudao_score_017_xydz", "爱信小贷-互联网行为子分(PD0017S968)", DataSourceEnum.Pudao, "pudaoFacade", CacheKeyEnum.Pudao_score_017_xydz, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-06-27 15:55:33"),

    Dianhuabang_mobile3_innet("dianhuabang_mobile3_innet", "电话邦-在网时长", DataSourceEnum.Dianhuabang, "dianhuabangFacade", CacheKeyEnum.Dianhuabang_mobile3_innet, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-07-13 10:25:33"),
    Dianhuabang_is_switch("dianhuabang_is_switch", "电话邦-携号转网", DataSourceEnum.Dianhuabang, "dianhuabangFacade", CacheKeyEnum.Dianhuabang_is_switch, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-07-13 10:25:33"),

    BaiNiu_saic_fourElementsCheck("bainiu_saic_fourElementsCheck", "重庆白牛-四要素核验", DataSourceEnum.BaiNiu, "baiNiuFacade", CacheKeyEnum.BaiNiu_saic_fourElementsCheck, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-08-21 18:01:00"),
    BaiNiu_saic_person("bainiu_saic_person", "重庆白牛-企业投资任职探寻", DataSourceEnum.BaiNiu, "baiNiuFacade", CacheKeyEnum.BaiNiu_saic_person, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-08-21 18:01:00"),

    Jiaya_general_score_v6("jiaya_general_score_v6", "珈亚-反欺诈V6通用", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_general_score_v6, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-10-13 19:10:00"),
    Jiaya_highirr_score_v6("jiaya_highirr_score_v6", "珈亚-反欺诈V6高利率", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_highirr_score_v6, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-10-13 19:10:00"),
    Jiaya_tyfx_score_v01("jiaya_tyfx_score_v01", "珈亚-风险分层分", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_tyfx_score_v01, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-10-13 19:10:00"),
    Jiaya_general_score_v6_V3("jiaya_general_score_v6_V3", "珈亚-反欺诈V6通用_V3版", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_general_score_v6_V3, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-10 19:10:00"),
    Jiaya_highirr_score_v6_V3("jiaya_highirr_score_v6_V3", "珈亚-反欺诈V6高利率_V3版", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_highirr_score_v6_V3, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-10 19:10:00"),
    Jiaya_general_score_v6_V3_new("jiaya_general_score_v6_V3_new", "珈亚-反欺诈V6通用_V3版（新）", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_general_score_v6_V3_new, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-01-03 17:00:00"),
    Jiaya_highirr_score_v6_V3_new("jiaya_highirr_score_v6_V3_new", "珈亚-反欺诈V6高利率_V3版（新）", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_highirr_score_v6_V3_new, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-01-03 17:00:00"),
    Jiaya_lowirr_score_v6_V3_new("jiaya_lowirr_score_v6_V3_new", "珈亚-反欺诈V6低利率_V3版（新）", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_lowirr_score_v6_V3_new, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-04-11 17:30:00"),
    Jiaya_1201303_score("jiaya_1201303_score", "珈亚-定制模型分1201303", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_1201303_score, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-06-06 11:30:00"),
    Jiaya_1201304_score("jiaya_1201304_score", "珈亚-定制模型分1201304", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_1201304_score, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-06-06 11:30:00"),
    Jiaya_1201305_score("jiaya_1201305_score", "珈亚-定制模型分1201305", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_1201305_score, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-06-06 11:30:00"),
    Jiaya_1100101_score("jiaya_1100101_score", "珈亚_天域高斯分_A01", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.Jiaya_1100101_score, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-07-26 16:30:00"),
    gsa_shbz_v6_11("gsa_shbz_v6_11", "珈亚_百度v6_11", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.gsa_shbz_v6_11, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-10-15 14:36:18"),
    gsa_shbz_v5_09("gsa_shbz_v5_09", "珈亚_百度v5_09", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.gsa_shbz_v5_09, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-10-15 14:36:18"),
    gsa_shbz_v5_10("gsa_shbz_v5_10", "珈亚_百度v5_10", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.gsa_shbz_v5_10, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-10-15 14:36:18"),
    gsa_shbz_v5_11("gsa_shbz_v5_11", "珈亚_百度v5_11", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.gsa_shbz_v5_11, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-10-15 14:36:18"),
    gs_bdbz_score_v2("gs_bdbz_score_v2", "珈亚_度越分-A-4", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.gs_bdbz_score_v2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-10-17 17:48:29"),
    gs_bdbz_score_v3("gs_bdbz_score_v3", "珈亚_度越分-A-5", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.gs_bdbz_score_v3, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-11-05 14:01:41"),
    gs_btbd_score_v1("gs_btbd_score_v1", "珈亚_智通分_A06", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.gs_btbd_score_v1, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-02-20 17:20:08"),
    gs_btbd_score_v3("gs_btbd_score_v3", "珈亚_智通分_A08", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.gs_btbd_score_v3, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-02-20 17:20:08"),
    gs_bdbz_v18("gs_bdbz_v18", "珈亚_度越分-A-7", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.gs_bdbz_v18, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-02-28 20:53:55"),
    zt_a_v6("zt_a_v6", "珈亚_智通分_A06_1000", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.zt_a_v6, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-04-11 10:14:34"),
    zt_a_v8m("zt_a_v8m", "珈亚_智通分_A08_1000", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.zt_a_v8m, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-04-11 10:14:34"),
    jiaya_zt_d_v01_1("jiaya_zt_d_v01_1", "珈亚_智通分_D01", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.jiaya_zt_d_v01_1, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 23:45:10"),
    jiaya_zt_a_v8_3("jiaya_zt_a_v8_3", "珈亚_智通分_A08_3", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.jiaya_zt_a_v8_3, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 23:45:10"),
    jiaya_zt_d_v12("jiaya_zt_d_v12", "珈亚_智通分_D12", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.jiaya_zt_d_v12, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 23:45:10"),
    jiaya_zt_d_v14("jiaya_zt_d_v14", "珈亚_智通分_D14", DataSourceEnum.Jiaya, "jiaYaFacade", CacheKeyEnum.jiaya_zt_d_v14, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-20 23:45:10"),

    Taidixiong_mobile3_innet("taidixiong_mobile3_innet", "泰迪熊-运营商在网时长", DataSourceEnum.Taidixiong, "taidixiongFacade", CacheKeyEnum.Taidixiong_mobile3_innet, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-10-27 19:00:00"),
    Taidixiong_phone_inuse("taidixiong_phone_inuse", "泰迪熊-号码验真", DataSourceEnum.Taidixiong, "taidixiongFacade", CacheKeyEnum.Taidixiong_phone_inuse, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-03-13 16:00:00"),
    Taidixiong_mobileDuration_test("taidixiong_mobileDuration_test", "泰迪熊-运营商在网时长_test", DataSourceEnum.Taidixiong, "taidixiongFacade", CacheKeyEnum.Taidixiong_mobileDuration_test, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-04-09 17:00:00"),
    Taidixiong_huitong_subscore1("taidixiong_huitong_subscore1", "泰迪熊-汇通子分1", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huitong_subscore1, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-05-22 14:28:00"),
    Taidixiong_huitong_subscore2("taidixiong_huitong_subscore2", "泰迪熊-汇通子分2", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huitong_subscore2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-05-22 14:28:00"),

    Taidixiong_huitong_subscore5("taidixiong_huitong_subscore5", "泰迪熊-汇通子分5", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huitong_subscore5, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-07-29 17:53:00"),
    Taidixiong_huitong_subscore6("taidixiong_huitong_subscore6", "泰迪熊-汇通子分6", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huitong_subscore6, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-07-29 17:53:00"),
    Taidixiong_huitong_subscore7("taidixiong_huitong_subscore7", "泰迪熊-汇通子分7", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huitong_subscore7, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-11-05 13:50:50"),
    Taidixiong_huitong_subscore8("taidixiong_huitong_subscore8", "泰迪熊-汇通子分8", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huitong_subscore8, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-11-05 13:50:50"),
    Taidixiong_huitong_xfdz001("taidixiong_huitong_xfdz001", "泰迪熊-模型定制子分", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huitong_xfdz001, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-11-22 10:45:30"),
    Taidixiong_huixin_zhishu01("taidixiong_huixin_zhishu01", "泰迪熊-汇信指数01", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huixin_zhishu01, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-01-04 14:26:06"),
    Taidixiong_huitong_subscore12("taidixiong_huitong_subscore12", "泰迪熊-汇通子分12", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huitong_subscore12, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-02-17 10:06:25"),
    Taidixiong_huitong_subscore13("taidixiong_huitong_subscore13", "泰迪熊-汇通子分13", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huitong_subscore13, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-03-14 15:51:37"),
    Taidixiong_huitong_subscore19("taidixiong_huitong_subscore19", "泰迪熊-汇通子分19", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huitong_subscore19, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-05-28 23:13:51"),
    Taidixiong_huixin_zhishu02("taidixiong_huixin_zhishu02", "泰迪熊-汇信指数02", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_huixin_zhishu02, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-05-28 23:17:34"),
    Taidixiong_tongxun_zhishu01("taidixiong_tongxun_zhishu01", "泰迪熊-通讯行为指数子分1", DataSourceEnum.Taidixiong, "taidixiongFacadeV2", CacheKeyEnum.Taidixiong_tongxun_zhishu01, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-06-30 20:47:10"),

    Lanxiang_janus_invoke_v1("lanxiang_janus_invoke_v1", "蓝象-易诉分3", DataSourceEnum.Lanxiang, "lanxiangFacade", CacheKeyEnum.Lanxiang_janus_invoke_v1, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-11-15 19:48:00"),

    Aliyun_DescribePhoneNumberOnlineTime("aliyun_DescribePhoneNumberOnlineTime", "阿里云-在网时长", DataSourceEnum.Aliyun, "aliyunFacade", CacheKeyEnum.Aliyun_DescribePhoneNumberOnlineTime, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-19 19:48:00"),

    Jitui_mobile_factor3("jitui_mobile_factor3", "极推-三要素核验", DataSourceEnum.Jitui, "jituiFacade", CacheKeyEnum.Jitui_mobile_factor3, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-19 19:48:00"),
    Jitui_mobile_status("jitui_mobile_status", "极推-在网状态", DataSourceEnum.Jitui, "jituiFacade", CacheKeyEnum.Jitui_mobile_status, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2023-12-19 19:48:00"),

    Yinlianshangwu_mobile_innet("yinlianshangwu_mobile_innet", "银联商务-在网时长", DataSourceEnum.Yinlianshangwu, "yinlianshangwuFacade", CacheKeyEnum.Yinlianshangwu_mobile_innet, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-03-07 08:48:00"),
    Yinlianshangwu_factor3_check("yinlianshangwu_factor3_check", "银联商务-三要素核验详版", DataSourceEnum.Yinlianshangwu, "yinlianshangwuFacade", CacheKeyEnum.Yinlianshangwu_factor3_check, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-03-07 08:48:00"),

    Huadao_HDD079("huadao_HDD079", "华道-定制标签", DataSourceEnum.Huadao, "huadaoFacade", CacheKeyEnum.Huadao_HDD079, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-05-22 16:27:00"),
    Huadao_EMW053_MD5("huadao_EMW053_MD5", "华道-三要素核验", DataSourceEnum.Huadao, "huadaoFacade", CacheKeyEnum.Huadao_EMW053_MD5, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-06-13 16:27:00"),
    Huadao_EMW053_sha256("huadao_EMW053_sha256", "华道-三要素核验_sha256", DataSourceEnum.Huadao, "huadaoFacade", CacheKeyEnum.Huadao_EMW053_sha256, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-06-26 15:15:00"),
    Huadao_EMW053_mingwen("huadao_EMW053_mingwen", "华道-三要素核验_mingwen", DataSourceEnum.Huadao, "huadaoFacade", CacheKeyEnum.Huadao_EMW053_mingwen, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-06-26 15:15:00"),
    Huadao_HDD080("huadao_HDD080", "华道-营销定制标签 V2", DataSourceEnum.Huadao, "huadaoFacade", CacheKeyEnum.Huadao_HDD080, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-05-19 09:59:00"),

    Yinlianzhice_merchant("yinlianzhice_merchant", "银联智策收入画像", DataSourceEnum.Yinlianzhice, "yinlianzhiceFacade", CacheKeyEnum.Yinlianzhice_merchant, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-07-15 17:15:00"),

    yujian_aft_score_aft_v4("yujian_aft_score_aft_v4", "驭鉴_洞察分aft_v4", DataSourceEnum.YuJian, "yuJianFacade", CacheKeyEnum.yujian_aft_score_aft_v4, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-10-30 15:40:29"),
    yujian_aft_score_aft_v4up1("yujian_aft_score_aft_v4up1", "驭鉴_洞察分aft_v4up1", DataSourceEnum.YuJian, "yuJianFacade", CacheKeyEnum.yujian_aft_score_aft_v4up1, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-10-30 15:40:29"),
    yujian_aft_score_aft_v4up2("yujian_aft_score_aft_v4up2", "驭鉴_洞察分aft_v4up2", DataSourceEnum.YuJian, "yuJianFacade", CacheKeyEnum.yujian_aft_score_aft_v4up2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-10-30 15:40:29"),
    yujian_submodel_score("yujian_submodel_score", "驭鉴_业务风险识别子分", DataSourceEnum.YuJian, "yuJianFacade", CacheKeyEnum.yujian_submodel_score, ReportTypeStatusEnum.valid, BillingMethodEnum.query, "2024-11-22 10:57:12"),
    yujian_aft_score_aft_v3("yujian_aft_score_aft_v3", "驭鉴_洞察分aft_v3", DataSourceEnum.YuJian, "yuJianFacade", CacheKeyEnum.yujian_aft_score_aft_v3, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-05-27 22:04:38"),

    tonglian_xtys_data_mx_qlh_model01("xtys_data_mx_qlh_model01", "通联支付_定制模型01", DataSourceEnum.Tonglian, "tonglianFacade", CacheKeyEnum.xtys_data_mx_qlh_model01, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-05-28 17:52:44"),
    tonglian_xtys_data_mx_qlh_model02("xtys_data_mx_qlh_model02", "通联支付_定制模型02", DataSourceEnum.Tonglian, "tonglianFacade", CacheKeyEnum.xtys_data_mx_qlh_model02, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-05-28 17:52:44"),

    MODEL_S0301007("model_S0301007", "S0301007", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2024-11-13 15:41:37"),
    MODEL_T02Y03A04("model_T02Y03A04", "T02Y03A04", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2024-11-14 17:30:35"),
    MODEL_Y01SUBV2("model_Y01SUBV2", "Y01SUBV2", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2024-11-28 16:10:47"),
    MODEL_OverdueReminderLevel("model_OverdueReminderLevel", "逾期提醒等级", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-02-25 16:40:12"),
    MODEL_TSLHPY01S("model_TSLHPY01S", "TSLHPY01S", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-02-28 20:36:45"),
    MODEL_TST10D07("model_TST10D07", "TST10D07", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-02-28 20:36:49"),
    MODEL_st_delinq_score("model_st_delinq_score", "短期逾期分", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-03-05 15:26:30"),
    MODEL_lt_delinq_score("model_lt_delinq_score", "长期逾期分", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-03-05 15:26:30"),
    MODEL_mt_delinq_score("model_mt_delinq_score", "中期逾期分", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-03-05 15:26:30"),
    MODEL_new_delinq_score("model_new_delinq_score", "新客逾期分", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-03-05 15:26:30"),
    MODEL_old_delinq_score("model_old_delinq_score", "老客逾期分", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-03-05 15:26:30"),
    MODEL_new_delinq_plus("model_new_delinq_plus", "新客逾期分plus", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-04-15 16:34:03"),

    MODEL_te_30_360("model_te_30_360", "调额_30~360", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-04-03 14:03:42"),
    MODEL_te_360("model_te_360", "调额_360+", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-04-03 14:03:42"),
    MODEL_te_xt("model_te_xt", "调额_信托", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-04-03 14:03:42"),
    MODEL_xhk("model_xhk", "新获客", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-04-03 14:03:42"),
    MODEL_zr_360("model_zr_360", "准入_360+", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-04-03 14:03:42"),
    MODEL_zr_jq("model_zr_jq", "准入_结清", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-04-03 14:03:42"),
    MODEL_zr_xt("model_zr_xt", "准入_信托30+", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-04-03 14:03:42"),
    MODEL_teddy_3mon_v1("model_teddy_3mon_v1", "teddy_3mon_v1", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-05-27 23:32:08"),
    MODEL_teddy_6mon_v1("model_teddy_6mon_v1", "teddy_6mon_v1", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-05-27 23:32:08"),
    MODEL_teddy_12mon_v1("model_teddy_12mon_v1", "teddy_12mon_v1", DataSourceEnum.MODEL, "modelFacade", null, ReportTypeStatusEnum.valid, null, "2025-05-27 23:32:08"),

    OverdueIndex("OverdueIndex", "逾期指数", DataSourceEnum.MODEL, "overdueIndexFacade", null, ReportTypeStatusEnum.valid, null, "2025-01-04 19:15:31"),
    OverdueIndexVariable("OverdueIndexVariable", "逾期指数基础变量库", DataSourceEnum.MODEL, "overdueIndexVariableFacade", null, ReportTypeStatusEnum.valid, null, "2025-08-16 14:49:10"),

    maisui_v1_phone_stat_sms("maisui_v1_phone_stat_sms", "麦穗_通讯统计特征v1", DataSourceEnum.Maisuitx,"maisuitxFacade", CacheKeyEnum.maisui_v1_phone_stat_sms, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2024-12-26 09:27:40"),
    maisui_v1_phone_stat_sms_v2("maisui_v1_phone_stat_sms_v2", "麦穗_通讯统计特征v2", DataSourceEnum.Maisuitx,"maisuitxFacade", CacheKeyEnum.maisui_v1_phone_stat_sms_v2, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-05-27 23:14:03"),

    guanshu_getLevelXFZHF01("guanshu_getLevelXFZHF01", "管数_企业经营稳定性评分", DataSourceEnum.Guanshu,"guanshuFacade", CacheKeyEnum.guanshu_getLevelXFZHF01, ReportTypeStatusEnum.valid, BillingMethodEnum.find, "2025-07-21 11:39:37"),
    ;

    final String name;
    final String desc;
    final DataSourceEnum dataSource;
    final String dataSourceFacadeName;
    final CacheKeyEnum cacheKeyEnum;
    final ReportTypeStatusEnum status;
    final BillingMethodEnum billingMethod;
    final String createTime;

    private static final Map<DataSourceEnum, List<ReportTypeEnum>> enumsMap = new HashMap<>();

    static {
        for (ReportTypeEnum d : ReportTypeEnum.values()) {
            List<ReportTypeEnum> list = enumsMap.computeIfAbsent(d.getDataSource(), k -> new ArrayList<>());
            list.add(d);
        }
    }

    private static final Map<String, ReportTypeEnum> reportTypeByNameMap = Arrays.stream(ReportTypeEnum.values())
            .collect(Collectors.toMap(ReportTypeEnum::getName, Function.identity()));


    public static List<ReportTypeEnum> getReportTypeEnums(DataSourceEnum e) {
        return enumsMap.get(e);
    }

    public static ReportTypeEnum getReportTypeEnums(String dsCode, String reportType) {
        ReportTypeEnum reportTypeEnum = reportTypeByNameMap.get(reportType);
        if (reportTypeEnum != null && StrUtil.equals(reportTypeEnum.getDataSource().name(), dsCode)) {
            return reportTypeEnum;
        }
        return null;
    }

    public static ReportTypeEnum fromName(String name) {
        return Optional.ofNullable(reportTypeByNameMap.get(name))
                .orElseThrow(() -> new IllegalArgumentException("未识别报告编码:" + name));
    }
}
