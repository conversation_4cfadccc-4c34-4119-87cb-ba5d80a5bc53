gzip_types application/x-javascript text/css;
fastcgi_intercept_errors on;
error_page 404 /404;
# $$HOME$$ 路径为 /opt/static

location ~* \.html$ {
    add_header Cache-Control "no-store, no-cache, private";
    root /opt/static/;
}

location = /activity {
 try_files $uri $uri/ /activity-www/index.html;
}

location = /favicon.ico {
  try_files $uri $uri/ /activity-www/favicon.ico;
}

location ^~ /activity/ {
   try_files $uri $uri/ /activity-www/index.html;
}

location /Status/ {
  alias /opt/deploy/;
}

location / {
  root /opt/static/;
}
