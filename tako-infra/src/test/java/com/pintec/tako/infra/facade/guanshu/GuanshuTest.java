package com.pintec.tako.infra.facade.guanshu;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pintec.tako.infra.facade.guanshu.util.GuanshuUtil;
import org.junit.jupiter.api.Test;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;

public class GuanshuTest {

    private static final String CLIENT_ID = "qlhzx";
    private static final String AES_KEY = "qlhzxaes";
    private static final String API_URL = "http://************:18899/api/cc/getLevelXFZHF01";

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testCompanyStabilityScore() throws Exception {
        String companyName = "国投甘肃售电有限公司";
        String usci = "91620100MA7190544W";
        String pname = "黄基玮";
        String certiCode = "440512200008190015";
        String phone = "13144291467";

        // AES加密测试数据
        String encryptedCompanyName = GuanshuUtil.aesEncrypt(AES_KEY, companyName);
        String encryptedUsci = GuanshuUtil.aesEncrypt(AES_KEY, usci);
        String encryptedPname = GuanshuUtil.aesEncrypt(AES_KEY, pname);
        String encryptedCertiCode = GuanshuUtil.aesEncrypt(AES_KEY, certiCode);
        String encryptedPhone = GuanshuUtil.aesEncrypt(AES_KEY, phone);

        // 构建请求参数
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("companyName", encryptedCompanyName);
        params.add("USCI", encryptedUsci);
        params.add("pname", encryptedPname);
        params.add("certiCode", encryptedCertiCode);
        params.add("phone", encryptedPhone);
        params.add("clientId", CLIENT_ID);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("charset", "UTF-8");

        // 创建请求实体
        HttpEntity<MultiValueMap<String, String>> requestEntity =
                new HttpEntity<>(params, headers);

        // 发送POST请求
        System.out.println("发送请求到: " + API_URL);
        System.out.println("请求参数: " + params);

        ResponseEntity<String> response = restTemplate.exchange(
                API_URL,
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        System.out.println("response = " + response);

    }

    /**
     * 处理错误响应
     */
    private void handleErrorResponse(String resCode, String resDesc) {
        switch (resCode) {
            case "9999":
                System.out.println("接口调用失败");
                break;
            case "0009":
                System.out.println("系统异常");
                break;
            case "0999":
                System.out.println("未查询到数据");
                break;
            case "1111":
                System.out.println("用户不存在");
                break;
            case "1112":
                System.out.println("请求地址错误");
                break;
            case "1116":
                System.out.println("缺失必须的参数");
                break;
            case "0080":
                System.out.println("没有接口访问权限");
                break;
            case "4404":
                System.out.println("client id不存在");
                break;
            case "4400":
                System.out.println("缺少必须的参数");
                break;
            case "4401":
                System.out.println("不允许访问的IP");
                break;
            case "4402":
                System.out.println("AES加密不正确");
                break;
            case "4500":
                System.out.println("系统内部异常");
                break;
            default:
                System.out.println("未知错误码: " + resCode);
                break;
        }
    }

    /**
     * AES加密方法
     *
     * @param plainText 明文
     * @param key       密钥
     * @return 加密后的Base64字符串
     */
    private String aesEncrypt(String plainText, String key) throws Exception {
        // 确保密钥长度为16位 (AES-128)
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        if (keyBytes.length != 16) {
            // 如果密钥不足16位，用0填充；如果超过16位，截取前16位
            byte[] paddedKey = new byte[16];
            System.arraycopy(keyBytes, 0, paddedKey, 0, Math.min(keyBytes.length, 16));
            keyBytes = paddedKey;
        }

        SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);

        byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    /**
     * AES解密方法（用于调试验证）
     *
     * @param encryptedText 加密的Base64字符串
     * @param key           密钥
     * @return 解密后的明文
     */
    private String aesDecrypt(String encryptedText, String key) throws Exception {
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        if (keyBytes.length != 16) {
            byte[] paddedKey = new byte[16];
            System.arraycopy(keyBytes, 0, paddedKey, 0, Math.min(keyBytes.length, 16));
            keyBytes = paddedKey;
        }

        SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, secretKey);

        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /**
     * 测试AES加解密功能
     */
    @Test
    public void testAesEncryptDecrypt() {
        try {
            String original = "测试数据";
            String encrypted = aesEncrypt(original, AES_KEY);
            String decrypted = aesDecrypt(encrypted, AES_KEY);

            System.out.println("原文: " + original);
            System.out.println("加密: " + encrypted);
            System.out.println("解密: " + decrypted);

            assertEquals(original, decrypted, "加解密结果应该一致");

        } catch (Exception e) {
            fail("AES加解密测试失败: " + e.getMessage());
        }
    }
}
