package com.pintec.tako.infra.facade.tonglian;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import com.alibaba.fastjson2.JSON;
import com.pintec.tako.infra.facade.tonglian.util.TonglianUtil;
import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

public class TonglianTest {

    private static final Logger log = LoggerFactory.getLogger(TonglianTest.class);

    private static final String apiUrl = "https://api.allintrust.cn";

    private static final String merId = "18011010002000048";
    private static final String secretKey = "18011010002000048";
    private static final String tenantId = "1221897659256672256";
    private static final String appKey = "qlhkey";

    private static final String qilehuiPrivateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJAhJaY8aGSZ1UCzs731QVH5x+C2QWju38X9KU4AhX7Y560//S03UJshkjYOwpuVedhHBVNLxlqnsNVlVfJvuogopR5yIY9v25Me+/P3PyZJIjeKVWRMwQGoWdb+2zDbkrROD68g9DOfBHsu0WYudz/ffLPb3jzs/YdWUCESFYhvAgMBAAECgYAOvR0FArYS3OUBBf9npFi8LHoDTDqkeOewRE3A2MPOBMSto6wHv4Qs1leF01I/p8ml03QYywMV39fGUrkMdDQpCHA+tKyevDXUTTXbQeg9qKLcfmDY41Fqq9oc7b5VHqAjSbOk2Zt3IqyyKoosOvNsbNqpCO9FFuyAgqabAdkE8QJBAKo5joW2BqtLe+V6xgM9Ayb+2F5jUfEI1Gy1h29YhCKNleNFEmbBIskjJgDqgyQgkupkqTPT4klTQL8dTEsihBECQQDYwWBBPxmhH/pICHk09WbiEnhnxUEz0nuutDdSn/JDxCcgTPTNsbJ/uTr6lp8bIjiYGk5SSTCOZ3gVa8xWScR/AkB2dNMvTTpa3Y58ZPGCs2SMMHAVTvxoxYI9nB2BaJGbEfgqTZi+lMxGOAVsQcW80EYsgpwUivA/ooW9+P9skbphAkABjfF4xlESvL34wkbMQG5KdMN0Sw/apUnJyPUUnJ0KK1/0dVjUGNAk5lkr7uSZNzecr6OcJ3Y3rzpHPQysGb9VAkBW+d9rqRlgH+hg4AcIjtYvVWUXZ7OMFL4pmX8u7GaEXSEjw5E8mhPRmHJEr8RUYCTmJd4UHM2X2ZjRhtyBK3/y";
    private static final String tonglianPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCOnMuPRe6ghvmWZjsEANODKMTRj0Mv/bbla23b03NcrJgBD/trnIJHgKXyvAfefVZ+8KMpON4odW36Nt/OkcTQjNXPi5npU6125xCnVN7jL4ugUEgDcBQ9sepgY2pkEvCOPq+djKqFDFkWpnTuoaEb+boqOJjNYYbjxFvz3cZC6wIDAQAB";

    private static final String productCode = "xtys-data-mx-qlh-model01";

    @Test
    void test() {
        String timestamp = System.currentTimeMillis() + "";
        TreeMap<String, String> paramMap = new TreeMap<>();
        paramMap.put("merId", merId);
        paramMap.put("productCode", productCode);
        paramMap.put("timestamp", timestamp);
        paramMap.put("custOrderId", "00000001");
        paramMap.put("encryptType", "02");
        // paramMap.put("idCard", "6e80bc68acaafa4e5270dd404856a54e");
        paramMap.put("phoneNo", "630c1e755dd066ccb9d1b42b76135408");

        log.info("paramMap: {}", JSON.toJSONString(paramMap));

        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("x_allintrust_client_type", "api");
        headerMap.put("x_allintrust_timestamp", timestamp);
        headerMap.put("x_allintrust_tenant", tenantId);
        headerMap.put("x_allintrust_app_key", appKey);
        headerMap.put("x_allintrust_key", secretKey);
        headerMap.put("x_allinpay_key", merId);
        headerMap.put("x_allintrust_prod_code", productCode);
        headerMap.put("x_allintrust_nonce", IdUtil.fastSimpleUUID());
        headerMap.put("x_allintrust_sign_type", "RSA");
        headerMap.put("encrypt_type", "RSA");

        String signContent = TonglianUtil.buildSignContent(paramMap, true, true, tonglianPublicKey);
        byte[] signByte = SecureUtil.sign(SignAlgorithm.SHA1withRSA, qilehuiPrivateKey, null).sign(signContent + "&" + timestamp);
        String sign = Base64.encodeBase64String(signByte);
        headerMap.put("x_allintrust_sign", sign);

        log.info("headerMap: {}", JSON.toJSONString(headerMap));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        for (Map.Entry<String, Object> entry : headerMap.entrySet()) {
            headers.set(entry.getKey(), String.valueOf(entry.getValue()));
        }
        HttpEntity<String> requestEntity = new HttpEntity<>(signContent, headers);
        ResponseEntity<String> responseEntity = new RestTemplate().postForEntity(apiUrl + "/ys-modeling-api/v2/trans/priv_modeling", requestEntity, String.class);
        log.info("responseEntity: {}", responseEntity);
    }

}
