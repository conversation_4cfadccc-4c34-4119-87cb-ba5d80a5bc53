package com.pintec.tako.infra.facade.taidixiong.resp;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.constant.Constant;
import com.pintec.tako.common.enums.DataTypeEnum;
import com.pintec.tako.common.enums.ParamTypeEnum;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.domain.datasource.entity.Parameter;
import com.pintec.tako.infra.facade.maisuitx.resp.PhoneSmsStatV2Resp;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Data
public class TaidixiongTongxunZhishu01Resp extends TaidixiongHuitongSubscoreResp {
	private static final String OUT_KEY = "taidixiong_tongxun_zhishu01";

	@CommentDesc("")
	private String taidixiong_tongxun_zhishu01;

	@CommentDesc("通讯活跃度子分（新客）")
	private BigDecimal ncomm_act_score = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("通讯活跃度子分（老客）")
	private BigDecimal ocomm_act_score = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月通话次数_网络")
	private BigDecimal call_num_sum_circle_12 = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月通话天数_网络")
	private BigDecimal m1_call_day_distinct_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月通话号码数_网络")
	private BigDecimal m1_call_num_distinct_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月有通话月份数_网络")
	private BigDecimal m3_has_call_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月通话次数_网络")
	private BigDecimal m3_call_num_sum_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月通话天数_网络")
	private BigDecimal m3_call_day_distinct_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月通话号码数_网络")
	private BigDecimal m3_call_num_distinct_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月月均通话次数_网络")
	private BigDecimal m3_call_num_avg_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月通话次数标准差_网络")
	private BigDecimal m3_call_num_std_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月是否有通话_网络")
	private BigDecimal m6_has_call_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月通话次数_网络")
	private BigDecimal m6_call_num_sum_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月通话天数_网络")
	private BigDecimal m6_call_day_distinct_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月通话号码数_网络")
	private BigDecimal m6_call_num_distinct_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月月均通话次数_网络")
	private BigDecimal m6_call_num_avg_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月通话次数标准差_网络")
	private BigDecimal m6_call_num_std_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近12个月是否有通话_网络")
	private BigDecimal m12_has_call_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近12个月通话次数_网络")
	private BigDecimal m12_call_num_sum_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近12个月通话天数_网络")
	private BigDecimal m12_call_day_distinct_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近12个月通话号码数_网络")
	private BigDecimal m12_call_num_distinct_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近12个月月均通话次数_网络")
	private BigDecimal m12_call_num_avg_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("短期通话次数占比趋势_网络")
	private BigDecimal st_call_num_mom_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("长期通话次数占比趋势_网络")
	private BigDecimal lt_call_num_mom_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("短期通话次数变化趋势_网络")
	private BigDecimal st_call_num_tend_circle = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("长期通话次数变化趋势_网络")
	private BigDecimal lt_call_num_tend_circle = Constant.DEFAULT_MODEL_VALUE;

	@CommentDesc("近1个月收到法院短信机构数")
	private BigDecimal m1_court_sms_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月收到调解中心短信机构数")
	private BigDecimal m1_mediation_center_sms_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月收到法院短信机构数")
	private BigDecimal m3_court_sms_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月收到调解中心短信机构数")
	private BigDecimal m3_mediation_center_sms_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月收到法院短信机构数")
	private BigDecimal m6_court_sms_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月收到调解中心短信机构数")
	private BigDecimal m6_mediation_center_sms_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月与贷后委托主叫通话次数")
	private BigDecimal m1_sus_entrustment_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月与法院主叫通话次数")
	private BigDecimal m1_court_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月与律所主叫通话次数")
	private BigDecimal m1_law_firm_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月与调解中心主叫通话次数")
	private BigDecimal m1_mediation_center_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月与贷后委托主叫通话次数")
	private BigDecimal m3_sus_entrustment_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月与法院主叫通话次数")
	private BigDecimal m3_court_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月与律所主叫通话次数")
	private BigDecimal m3_law_firm_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月与调解中心主叫通话次数")
	private BigDecimal m3_mediation_center_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月与贷后委托主叫通话次数")
	private BigDecimal m6_sus_entrustment_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月与法院主叫通话次数")
	private BigDecimal m6_court_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月与律所主叫通话次数")
	private BigDecimal m6_law_firm_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月与调解中心主叫通话次数")
	private BigDecimal m6_mediation_center_called_call_num_sum = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月与法院主叫通话机构数")
	private BigDecimal m1_court_called_call_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月与律所主叫通话机构数")
	private BigDecimal m1_law_firm_called_call_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近1个月与调解中心主叫通话机构数")
	private BigDecimal m1_mediation_center_called_call_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月与法院主叫通话机构数")
	private BigDecimal m3_court_called_call_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月与律所主叫通话机构数")
	private BigDecimal m3_law_firm_called_call_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近3个月与调解中心主叫通话机构数")
	private BigDecimal m3_mediation_center_called_call_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月与法院主叫通话机构数")
	private BigDecimal m6_court_called_call_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月与律所主叫通话机构数")
	private BigDecimal m6_law_firm_called_call_org_num_dist = Constant.DEFAULT_MODEL_VALUE;
	@CommentDesc("近6个月与调解中心主叫通话机构")
	private BigDecimal m6_mediation_center_called_call_org_num_dist = Constant.DEFAULT_MODEL_VALUE;

	public static TaidixiongTongxunZhishu01Resp parse(String respData) {
		TaidixiongTongxunZhishu01Resp resp = TaidixiongHuitongSubscoreResp.parseContent(respData, TaidixiongTongxunZhishu01Resp.class, OUT_KEY, "T0401001");
		String taidixiong_tongxun_zhishu01 = (String)resp.getDataMap().get(OUT_KEY);

		TaidixiongTongxunZhishu01Resp defaultValueResp = new TaidixiongTongxunZhishu01Resp();
		Map<String, Object> defaultValueMap = JsonUtil.parse(JsonUtil.str(defaultValueResp), new TypeReference<Map<String, Object>>() {});
		resp.getDataMap().putAll(defaultValueMap);

		if (taidixiong_tongxun_zhishu01 == null || "-1".equals(taidixiong_tongxun_zhishu01)) {
			return resp;
		}

		Map<String, Object> map = JsonUtil.parse(taidixiong_tongxun_zhishu01, new TypeReference<Map<String, Object>>() {});
		map.forEach((k, v) -> {
			if (v != null && !Constant.NEGATIVE_ONE.equals(v)) {
				resp.getDataMap().put(k, v);
			}
		});

		return resp;
	}

}
