package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongTotalTravelTimeResp extends LiantongResp {

    @CommentDesc("")
    private String liantong_totalTravelTime;

    public static LiantongTotalTravelTimeResp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongTotalTravelTimeResp.class, "liantong_totalTravelTime");
    }
} 