package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongPtBuyingTicketsCountResp extends LiantongResp {

    @CommentDesc("")
    private String liantong_ptBuyingTicketsCount;

    public static LiantongPtBuyingTicketsCountResp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongPtBuyingTicketsCountResp.class, "liantong_ptBuyingTicketsCount");
    }
} 