package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongGdcRideratioResp extends LiantongResp {

    @CommentDesc("")
    private String liantong_gdcRideratio;

    public static LiantongGdcRideratioResp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongGdcRideratioResp.class, "liantong_gdcRideratio");
    }
} 