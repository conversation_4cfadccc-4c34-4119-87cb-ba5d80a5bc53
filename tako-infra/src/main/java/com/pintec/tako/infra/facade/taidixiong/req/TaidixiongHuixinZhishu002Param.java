package com.pintec.tako.infra.facade.taidixiong.req;

import com.pintec.tako.infra.facade.taidixiong.config.TaidixiongProperty;
import com.pintec.tako.infra.facade.taidixiong.enums.TaidixiongApiEnum;
import lombok.Data;


@Data
public class TaidixiongHuixinZhishu002Param extends TaidixiongMobileParam {
	public static TaidixiongHuixinZhishu002Param parse(String paramJson) {
		return TaidixiongMobileParam.parse(param<PERSON>son, TaidixiongHuixinZhishu002Param.class);
	}

	@Override
	protected String getProductKey(TaidixiongProperty taidixiongProperty) {
		return "T0301002";
	}

	@Override
	protected TaidixiongApiEnum getApiEnum() {
		return TaidixiongApiEnum.Taidixiong_huixin_zhishu02;
	}
}
