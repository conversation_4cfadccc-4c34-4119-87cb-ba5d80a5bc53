package com.pintec.tako.infra.facade.taidixiong.req;

import com.pintec.tako.infra.facade.taidixiong.config.TaidixiongProperty;
import com.pintec.tako.infra.facade.taidixiong.enums.TaidixiongApiEnum;
import lombok.Data;


@Data
public class TaidixiongTongxunZhishu01Param extends TaidixiongMobileParam {
	public static TaidixiongTongxunZhishu01Param parse(String paramJson) {
		return TaidixiongMobileParam.parse(paramJson, TaidixiongTongxunZhishu01Param.class);
	}

	@Override
	protected String getProductKey(TaidixiongProperty taidixiongProperty) {
		return "T0401001";
	}

	@Override
	protected TaidixiongApiEnum getApiEnum() {
		return TaidixiongApiEnum.Taidixiong_tongxun_zhishu01;
	}
}
