package com.pintec.tako.infra.facade.jiaya.res;

import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.support.InvokeResponse;
import com.pintec.tako.infra.facade.jiaya.enums.JiaYaApiEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/01/12 00:03
 */
@Data
public class JiayaZtDV14Resp extends JiaYaGsResp {
	private static final String output_key = "zt_d_v14";
	@CommentDesc("")
	private BigDecimal zt_d_v14;

	public static InvokeResponse parse(String respData) {
		return JiaYaGsResp.parseGsaScore(respData, output_key, JiaYaApiEnum.jiaya_zt_d_v14);
	}
} 