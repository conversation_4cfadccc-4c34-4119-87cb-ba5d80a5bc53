package com.pintec.tako.infra.facade.tonglian.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "ds.tonglian")
public class TonglianProperty {

    private boolean mock;

    private String apiUrl;

    private String merId;
    private String secretKey;
    private String tenantId;
    private String appKey;

    private String qilehuiPrivateKey;
    private String tonglianPublicKey;

}