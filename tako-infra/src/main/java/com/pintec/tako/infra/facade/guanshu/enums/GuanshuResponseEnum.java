package com.pintec.tako.infra.facade.guanshu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GuanshuResponseEnum {

    CODE_0000("0000", "接口调用成功"),
    CODE_9999("9999", "接口调用失败"),
    CODE_0009("0009", "系统异常"),
    CODE_0999("0999", "未查询到数据"),
    CODE_1111("1111", "用户不存在"),
    CODE_1112("1112", "请求地址错误"),
    CODE_1116("1116", "缺失必须的参数"),
    CODE_0080("0080", "没有接口访问权限"),
    CODE_4404("4404", "client id不存在"),
    CODE_4400("4400", "缺少必须的参数"),
    CODE_4401("4401", "不允许访问的IP"),
    CODE_4402("4402", "AES加密不正确"),
    CODE_4500("4500", "系统内部异常"),
    ;

    final String code;
    final String desc;

    public static boolean isSuccess(String code) {
        return CODE_0000.getCode().equals(code);
    }
}
