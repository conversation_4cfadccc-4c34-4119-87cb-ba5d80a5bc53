package com.pintec.tako.infra.facade.jiaya.res;

import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.support.InvokeResponse;
import com.pintec.tako.infra.facade.jiaya.enums.JiaYaApiEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/01/11 23:55
 */
@Data
public class JiayaZtAV8_3Resp extends JiaYaGsResp {
	private static final String output_key = "zt_a_v8_3";
	@CommentDesc("")
	private BigDecimal zt_a_v8_3;

	public static InvokeResponse parse(String respData) {
		return JiaYaGsResp.parseGsaScore(respData, output_key, JiaYaApiEnum.jiaya_zt_a_v8_3);
	}
} 