package com.pintec.tako.infra.facade.taidixiong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

@Slf4j
@Data
public class TaidixiongHuitongSubscore19Resp extends TaidixiongHuitongSubscoreResp {
	private static final String key = "taidixiong_huitong_subscore19";

	@CommentDesc("")
	private BigDecimal taidixiong_huitong_subscore19;

	public static TaidixiongHuitongSubscore19Resp parse(String respData) {
		return TaidixiongHuitongSubscoreResp.parse(respData, key, "T0201019", TaidixiongHuitongSubscore19Resp.class);
	}
}
