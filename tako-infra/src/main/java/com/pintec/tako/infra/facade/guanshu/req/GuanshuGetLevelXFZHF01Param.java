package com.pintec.tako.infra.facade.guanshu.req;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.support.BaseReportRequest;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.common.util.ValidationUtil;
import com.pintec.tako.infra.facade.guanshu.util.GuanshuUtil;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
public class GuanshuGetLevelXFZHF01Param extends BaseReportRequest {


    @CommentDesc("企业名称")
    @NotBlank
    @Length(min = 32, max = 32, message = "仅支持MD5，长度必须为32位")
    private String companyName;

    @CommentDesc("企业信⽤代码")
    @NotBlank
    @Length(min = 32, max = 32, message = "仅支持MD5，长度必须为32位")
    @JSONField(serialize = false)
    private String companyCreditIdentifier;
    @JSONField(name = "USCI")
    private String USCI;

    @CommentDesc("姓名")
    @NotBlank
    @Length(min = 32, max = 32, message = "仅支持MD5，长度必须为32位")
    @JSONField(serialize = false)
    private String name;
    private String pname;

    @CommentDesc("身份证号")
    @NotBlank
    @Length(min = 32, max = 32, message = "仅支持MD5，长度必须为32位")
    @JSONField(serialize = false)
    private String idCard;
    private String certiCode;

    @CommentDesc("手机号")
    @NotBlank
    @Length(min = 32, max = 32, message = "仅支持MD5，长度必须为32位")
    private String phone;

    private String clientId;


    public static BaseReportRequest parse(String paramJson) {
        GuanshuGetLevelXFZHF01Param param = JsonUtil.parse(paramJson, GuanshuGetLevelXFZHF01Param.class);
        ValidationUtil.validate(param);

        param.setCompanyName(GuanshuUtil.aesEncrypt(param.getCompanyName()));
        param.setUSCI(GuanshuUtil.aesEncrypt(param.getCompanyCreditIdentifier()));
        param.setPname(GuanshuUtil.aesEncrypt(param.getName()));
        param.setCertiCode(GuanshuUtil.aesEncrypt(param.getIdCard()));
        param.setPhone(GuanshuUtil.aesEncrypt(param.getPhone()));
        param.setClientId(GuanshuUtil.clientId);

        return param;
    }


    @Override
    public String key() {
        return SecureUtil.md5(companyName + "-" + companyCreditIdentifier + "-" + name + "-" + idCard + "-" + phone);
    }
}
