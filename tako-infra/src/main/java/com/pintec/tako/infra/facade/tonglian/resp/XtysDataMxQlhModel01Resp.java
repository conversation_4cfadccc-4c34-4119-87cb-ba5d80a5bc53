package com.pintec.tako.infra.facade.tonglian.resp;

import cn.hutool.core.util.StrUtil;
import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.infra.facade.tonglian.enums.TonglianResponseEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class XtysDataMxQlhModel01Resp extends TonglianResp {

    public static final String OUT_KEY = "xtys_data_mx_qlh_model01";
    @CommentDesc("")
    private BigDecimal xtys_data_mx_qlh_model01;


    public static XtysDataMxQlhModel01Resp parse(String json) {
        if (StrUtil.isBlank(json)) {
            json = "{}";
        }
        XtysDataMxQlhModel01Resp resp = JsonUtil.parse(json, XtysDataMxQlhModel01Resp.class);
        resp.getDataMap().put(OUT_KEY, -1);
        if (TonglianResponseEnum.isSuccess(resp)) {
            resp.setApiInvokeSuccess(true);
            Data data = TonglianResp.data(resp);
            if (data != null) {
                BigDecimal val = data.getScore();
                if (val != null && BigDecimal.ZERO.compareTo(val) <= 0 && BigDecimal.ONE.compareTo(val) >= 0) {
                    resp.setBillingFlag(true);
                    resp.setOutFeatureCount(1);
                    resp.getDataMap().put(OUT_KEY, val);
                }
            }
        }
        return resp;
    }
}
