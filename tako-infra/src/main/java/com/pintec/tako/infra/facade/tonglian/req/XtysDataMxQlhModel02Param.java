package com.pintec.tako.infra.facade.tonglian.req;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.support.BaseReportRequest;
import com.pintec.tako.common.util.ValidationUtil;
import com.pintec.tako.infra.validation.jsr.constraints.Md5OrSha256;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class XtysDataMxQlhModel02Param extends BaseReportRequest {

    @NotBlank
    @JSONField(serialize = false)
    @Pattern(regexp = "^md5|MD5|sha256|SHA256$", message = "加密方式，仅支持md5与sha256")
    @CommentDesc("加密方式，仅支持md5，sha256")
    private String cryptoType;

    @CommentDesc("身份证号")
    @Md5OrSha256(required = false)
    private String idCard;

    /**
     * 手机号（md5加密/sha256加密）
     */
    @JSONField(serialize = false)
    @Md5OrSha256(required = false)
    @CommentDesc("手机号")
    private String mobile;

    private String encryptType;
    private String phoneNo;


    public static XtysDataMxQlhModel02Param parse(String paramJson) {
        XtysDataMxQlhModel02Param param = JSON.parseObject(paramJson, XtysDataMxQlhModel02Param.class);
        ValidationUtil.validate(param);
        if ("md5".equals(param.getEncryptType())) {
            param.setEncryptType("02");
        } else if ("sha256".equals(param.getEncryptType())) {
            param.setEncryptType("03");
        }
        if (param.getMobile() == null && param.getIdCard() == null) {
            throw new IllegalArgumentException("idCard 和 mobile 不能同时为空");
        }
        param.setCryptoType(null);
        param.setPhoneNo(param.getMobile());
        param.setMobile(null);
        return param;

    }

    @Override
    public String key() {
        return cryptoType + "_" + idCard + "_" + mobile;
    }
}
