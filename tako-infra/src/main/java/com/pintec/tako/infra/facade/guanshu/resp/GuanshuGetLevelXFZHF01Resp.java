package com.pintec.tako.infra.facade.guanshu.resp;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.constant.Constant;
import com.pintec.tako.common.support.InvokeResponse;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.infra.facade.guanshu.enums.GuanshuResponseEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Data
@Slf4j
public class GuanshuGetLevelXFZHF01Resp extends GuanshuResp {

    @CommentDesc("")
    private BigDecimal guanshu_getLevelXFZHF01;

    public static InvokeResponse parse(String respData) {
        if (StrUtil.isBlank(respData)) {
            respData = "{}";
        }
        GuanshuResp resp = JsonUtil.parse(respData, GuanshuResp.class);
        resp.getDataMap().put("guanshu_getLevelXFZHF01", "-1");

        if (GuanshuResponseEnum.isSuccess(resp.getResCode())) {
            resp.setApiInvokeSuccess(true);
            Optional.ofNullable(resp.getData())
                    .map(it -> it.get("result"))
                    .filter(Map.class::isInstance)
                    .map(it -> (Map<String, Object>) it)
                    .map(it -> it.get("level"))
                    .filter(GuanshuGetLevelXFZHF01Resp::isLevelInValidRange)
                    .ifPresent(level -> {
                        resp.setBillingFlag(true);
                        resp.setOutFeatureCount(1);
                        resp.getDataMap().put("guanshu_getLevelXFZHF01", new BigDecimal(level.toString()));
                    });
        }
        return resp;
    }

    private static boolean isLevelInValidRange(Object level) {
        if (level == null || !NumberUtil.isNumber(level.toString())) {
            return false;
        }
        try {
            BigDecimal levelValue = new BigDecimal(level.toString());
            return Constant.THREE_HUNDRED.compareTo(levelValue) <= 0
                    && Constant.SEVEN_HUNDRED_FIFTY.compareTo(levelValue) >= 0;
        } catch (NumberFormatException e) {
            log.warn("等级值格式不正确: {}", level);
            return false;
        }
    }
}
