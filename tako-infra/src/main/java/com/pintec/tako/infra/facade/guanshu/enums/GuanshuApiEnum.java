package com.pintec.tako.infra.facade.guanshu.enums;

import com.pintec.tako.common.enums.ParamTypeEnum;
import com.pintec.tako.common.enums.ReportTypeEnum;
import com.pintec.tako.common.support.BaseReportRequest;
import com.pintec.tako.common.support.InvokeResponse;
import com.pintec.tako.domain.datasource.entity.Parameter;
import com.pintec.tako.infra.facade.fulin.req.FulinDongjianGaoParam;
import com.pintec.tako.infra.facade.fulin.resp.FulinDongjianGaoResp;
import com.pintec.tako.infra.facade.guanshu.req.GuanshuGetLevelXFZHF01Param;
import com.pintec.tako.infra.facade.guanshu.resp.GuanshuGetLevelXFZHF01Resp;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by wenxuan.ding on 2022/9/15 10:42.
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum GuanshuApiEnum {
    guanshu_getLevelXFZHF01("/api/cc/getLevelXFZHF01",  "/guanshu_getLevelXFZHF01", ReportTypeEnum.guanshu_getLevelXFZHF01, GuanshuGetLevelXFZHF01Param::parse, GuanshuGetLevelXFZHF01Param.class, GuanshuGetLevelXFZHF01Resp::parse, GuanshuGetLevelXFZHF01Resp.class),


    ;

    final String api;
    final String mockApi;
    final ReportTypeEnum reportType;
    final Function<String, BaseReportRequest> paramBuilder;
    final Class<? extends BaseReportRequest> paramsClass;
    final Function<String, InvokeResponse> respBuilder;
    final Class<? extends InvokeResponse> respClass;

    public List<Parameter> getParams() {
        List<Parameter> params = Parameter.getParams(paramsClass, ParamTypeEnum.IN);
        List<Parameter> outParams = Parameter.getParams(respClass, ParamTypeEnum.OUT);
        params.addAll(outParams);
        return params;
    }

    private static final Map<ReportTypeEnum, GuanshuApiEnum> reportTypeMap =
            Arrays.stream(values()).collect(Collectors.toMap(GuanshuApiEnum::getReportType, Function.identity()));


    public static GuanshuApiEnum valueOf(ReportTypeEnum reportType) {
        GuanshuApiEnum apiEnum = reportTypeMap.get(reportType);
        if (apiEnum == null) {
            throw new IllegalArgumentException("Can't support:" + reportType);
        }
        return apiEnum;
    }
}
