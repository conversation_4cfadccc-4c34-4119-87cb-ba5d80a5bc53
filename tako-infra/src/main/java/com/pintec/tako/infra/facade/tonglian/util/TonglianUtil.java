package com.pintec.tako.infra.facade.tonglian.util;

import cn.hutool.core.map.MapUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import com.alibaba.fastjson2.JSON;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

public class TonglianUtil {

    public static String buildSignContent(TreeMap<String, String> sortedParams, boolean post, boolean encrypt, String tonglianPublicKey) {
        if (!post) {
            return buildSignContent(sortedParams, encrypt, tonglianPublicKey);
        }
        String jsonContent = JSON.toJSONString(sortedParams);
        if (!encrypt) {
            return jsonContent;
        }
        String encryptContent = SecureUtil.rsa(null, tonglianPublicKey).encryptBase64(jsonContent, KeyType.PublicKey);
        Map<String, String> map = new HashMap<>(2);
        map.put("biz_class", Map.class.getName());
        map.put("biz_content", encryptContent);
        jsonContent = JSON.toJSONString(map);
        return jsonContent;
    }


    public static String buildSignContent(final TreeMap<String, String> sortedParams, boolean encrypt, String tonglianPublicKey) {
        String content = MapUtil.join(sortedParams, "=", "&");
        if (!encrypt) {
            return content;
        }
        return SecureUtil.rsa(null, tonglianPublicKey).encryptBase64(content, KeyType.PublicKey);
    }
}
