package com.pintec.tako.infra.facade.model.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;

import java.lang.reflect.Field;
import java.math.BigDecimal;

@Data
@Slf4j
public class OverdueIndexVariableResp {

    @CommentDesc("calculated_yy_rounded")
    private BigDecimal calculated_yy_rounded;

    @CommentDesc("m1_od_num_sum")
    private Integer m1_od_num_sum;

    @CommentDesc("m1_m2a_od_num_sum")
    private Integer m1_m2a_od_num_sum;

    @CommentDesc("m1_od_orgcnt_dist")
    private Integer m1_od_orgcnt_dist;

    @CommentDesc("m1_m2a_od_orgcnt_dist")
    private Integer m1_m2a_od_orgcnt_dist;

    @CommentDesc("m3_od_num_sum")
    private Integer m3_od_num_sum;

    @CommentDesc("m3_m2a_od_num_sum")
    private Integer m3_m2a_od_num_sum;

    @CommentDesc("m3_od_orgcnt_dist")
    private Integer m3_od_orgcnt_dist;

    @CommentDesc("m3_m2a_od_orgcnt_dist")
    private Integer m3_m2a_od_orgcnt_dist;

    @CommentDesc("m6_od_num_sum")
    private Integer m6_od_num_sum;

    @CommentDesc("m6_m2a_od_num_sum")
    private Integer m6_m2a_od_num_sum;

    @CommentDesc("m6_od_orgcnt_dist")
    private Integer m6_od_orgcnt_dist;

    @CommentDesc("m6_m2a_od_orgcnt_dist")
    private Integer m6_m2a_od_orgcnt_dist;

    @CommentDesc("m12_od_num_sum")
    private Integer m12_od_num_sum;

    @CommentDesc("m12_m2a_od_num_sum")
    private Integer m12_m2a_od_num_sum;

    @CommentDesc("m12_od_orgcnt_dist")
    private Integer m12_od_orgcnt_dist;

    @CommentDesc("m12_m2a_od_orgcnt_dist")
    private Integer m12_m2a_od_orgcnt_dist;

    @CommentDesc("his_od_days_start")
    private Integer his_od_days_start;

    @CommentDesc("his_od_days_last")
    private Integer his_od_days_last;

    @CommentDesc("m12_od_date_start")
    private String m12_od_date_start;

    @CommentDesc("m12_od_date_last")
    private String m12_od_date_last;

    /**
     * 从CSV记录创建OverdueIndexVariableResp对象（Apache Commons CSV版本）
     * 使用反射自动映射字段，避免手动维护字段名
     */
    public static OverdueIndexVariableResp fromCsvRecord(CSVRecord csvRecord) {
        if (csvRecord == null) {
            return null;
        }

        try {
            OverdueIndexVariableResp resp = new OverdueIndexVariableResp();
            Field[] fields = OverdueIndexVariableResp.class.getDeclaredFields();

            for (Field field : fields) {
                // 跳过计算字段和静态字段
                if (field.getName().startsWith("m12_od_date_") ||
                    java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                    field.getName().equals("log")) {
                    continue;
                }

                field.setAccessible(true);
                String fieldName = field.getName();

                try {
                    // 检查CSV中是否包含该字段
                    if (!csvRecord.isSet(fieldName)) {
                        continue;
                    }

                    Object value = null;
                    Class<?> fieldType = field.getType();
                    String csvValue = csvRecord.get(fieldName);

                    if (csvValue == null || csvValue.trim().isEmpty()) {
                        continue;
                    }

                    if (fieldType == BigDecimal.class) {
                        value = new BigDecimal(csvValue);
                    } else if (fieldType == Integer.class) {
                        // 处理可能的小数点，先转为double再转为int
                        double doubleValue = Double.parseDouble(csvValue);
                        value = (int) doubleValue;
                    } else if (fieldType == String.class) {
                        value = csvValue;
                    }

                    if (value != null) {
                        field.set(resp, value);
                    }

                } catch (Exception e) {
                    log.warn("设置字段值失败: field={}, error={}", fieldName, e.getMessage());
                }
            }

            return resp;

        } catch (Exception e) {
            log.error("从CSV记录创建OverdueIndexVariableResp失败: {}", e.getMessage(), e);
            return null;
        }
    }

}
