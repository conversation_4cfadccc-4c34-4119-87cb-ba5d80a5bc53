package com.pintec.tako.infra.facade.model.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OverdueIndexVariableResp {

    @CommentDesc("calculated_yy_rounded")
    private BigDecimal calculated_yy_rounded;
    // TODO 以下字段参照calculated_yy_rounded完成
    // m1_od_num_sum
    // m1_m2a_od_num_sum
    // m1_od_orgcnt_dist
    // m1_m2a_od_orgcnt_dist
    // m3_od_num_sum
    // m3_m2a_od_num_sum
    // m3_od_orgcnt_dist
    // m3_m2a_od_orgcnt_dist
    // m6_od_num_sum
    // m6_m2a_od_num_sum
    // m6_od_orgcnt_dist
    // m6_m2a_od_orgcnt_dist
    // m12_od_num_sum
    // m12_m2a_od_num_sum
    // m12_od_orgcnt_dist
    // m12_m2a_od_orgcnt_dist
    // his_od_days_start 代表天数
    // his_od_days_last 代表天数
    // 增加2个输出的字段，代表日期字符串，m12_od_date_start、m12_od_date_last,字符串yyyy-MM-dd，逻辑是这样：
    // m12_od_date_start=当前日期-his_od_days_start，
    // m12_od_date_last=当前日期-his_od_days_last

}
