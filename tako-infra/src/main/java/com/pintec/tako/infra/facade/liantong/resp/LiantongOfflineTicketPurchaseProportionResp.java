package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongOfflineTicketPurchaseProportionResp extends LiantongResp {

    @CommentDesc("")
    private String liantong_offlineTicketPurchaseProportion;

    public static LiantongOfflineTicketPurchaseProportionResp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongOfflineTicketPurchaseProportionResp.class, "liantong_offlineTicketPurchaseProportion");
    }
} 