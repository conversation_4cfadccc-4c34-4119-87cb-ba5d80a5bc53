package com.pintec.tako.infra.facade.model.enums;

import com.pintec.tako.common.enums.ParamTypeEnum;
import com.pintec.tako.common.enums.ReportTypeEnum;
import com.pintec.tako.domain.datasource.entity.Parameter;
import com.pintec.tako.infra.facade.model.req.*;
import com.pintec.tako.infra.facade.model.resp.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.bouncycastle.cert.ocsp.Req;
import org.dmg.pmml.FieldName;
import org.dmg.pmml.PMML;
import org.jpmml.evaluator.Evaluator;
import org.jpmml.evaluator.ModelEvaluatorBuilder;
import org.jpmml.evaluator.ModelEvaluatorFactory;
import org.jpmml.model.PMMLUtil;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ModelEnum {

    MODEL_S0301007("S0301007", ReportTypeEnum.MODEL_S0301007, S0301007Req::parse, S0301007Req.class, S0301007Resp::parse, S0301007Resp.class, true),
    MODEL_T02Y03A04("T02Y03A04", ReportTypeEnum.MODEL_T02Y03A04, T02Y03A04Req::parse, T02Y03A04Req.class, T02Y03A04Resp::parse, T02Y03A04Resp.class, true),
    MODEL_Y01SUBV2("Y01SUBV2", ReportTypeEnum.MODEL_Y01SUBV2, Y01SUBV2Req::parse, Y01SUBV2Req.class, Y01SUBV2Resp::parse, Y01SUBV2Resp.class, true),
    MODEL_OverdueReminderLevel("OverdueReminderLevel", ReportTypeEnum.MODEL_OverdueReminderLevel, OverdueReminderLevelReq::parse, OverdueReminderLevelReq.class, OverdueReminderLevelResp::parse, OverdueReminderLevelResp.class, true),
    MODEL_TSLHPY01S("TSLHPY01S", ReportTypeEnum.MODEL_TSLHPY01S, TSLHPY01SReq::parse, TSLHPY01SReq.class, TSLHPY01SResp::parse, TSLHPY01SResp.class, true),
    MODEL_TST10D07("TST10D07", ReportTypeEnum.MODEL_TST10D07, TST10D07Req::parse, TST10D07Req.class, TST10D07Resp::parse, TST10D07Resp.class, true),
    MODEL_st_delinq_score("st_delinq_score", ReportTypeEnum.MODEL_st_delinq_score, StDelinqScoreReq::parse, StDelinqScoreReq.class, StDelinqScoreResp::parse, StDelinqScoreResp.class, true),
    MODEL_lt_delinq_score("lt_delinq_score", ReportTypeEnum.MODEL_lt_delinq_score, LtDelinqScoreReq::parse, LtDelinqScoreReq.class, LtDelinqScoreResp::parse, LtDelinqScoreResp.class, true),
    MODEL_mt_delinq_score("mt_delinq_score", ReportTypeEnum.MODEL_mt_delinq_score, MtDelinqScoreReq::parse, MtDelinqScoreReq.class, MtDelinqScoreResp::parse, MtDelinqScoreResp.class, true),
    MODEL_new_delinq_score("new_delinq_score", ReportTypeEnum.MODEL_new_delinq_score, NewDelinqScoreReq::parse, NewDelinqScoreReq.class, NewDelinqScoreResp::parse, NewDelinqScoreResp.class, true),
    MODEL_old_delinq_score("old_delinq_score", ReportTypeEnum.MODEL_old_delinq_score, OldDelinqScoreReq::parse, OldDelinqScoreReq.class, OldDelinqScoreResp::parse, OldDelinqScoreResp.class, true),
    MODEL_new_delinq_plus("new_delinq_plus", ReportTypeEnum.MODEL_new_delinq_plus, NewDelinqPlusReq::parse, NewDelinqPlusReq.class, NewDelinqPlusResp::parse, NewDelinqPlusResp.class, true),

    MODEL_te_30_360("te_30_360", ReportTypeEnum.MODEL_te_30_360, Te30360Req::parse, Te30360Req.class, Te30360Resp::parse, Te30360Resp.class, true),
    MODEL_te_360("te_360", ReportTypeEnum.MODEL_te_360, Te360Req::parse, Te360Req.class, Te360Resp::parse, Te360Resp.class, true),
    MODEL_te_xt("te_xt", ReportTypeEnum.MODEL_te_xt, TeXtReq::parse, TeXtReq.class, TeXtResp::parse, TeXtResp.class, true),
    MODEL_xhk("xhk", ReportTypeEnum.MODEL_xhk, XhkReq::parse, XhkReq.class, XhkResp::parse, XhkResp.class, true),
    MODEL_zr_360("zr_360", ReportTypeEnum.MODEL_zr_360, Zr360Req::parse, Zr360Req.class, Zr360Resp::parse, Zr360Resp.class, true),
    MODEL_zr_jq("zr_jq", ReportTypeEnum.MODEL_zr_jq, ZrJqReq::parse, ZrJqReq.class, ZrJqResp::parse, ZrJqResp.class, true),
    MODEL_zr_xt("zr_xt", ReportTypeEnum.MODEL_zr_xt, ZrXtReq::parse, ZrXtReq.class, ZrXtResp::parse, ZrXtResp.class, true),

    MODEL_teddy_3mon_v1("teddy_3mon_v1", ReportTypeEnum.MODEL_teddy_3mon_v1, Teddy3MonV1Req::parse, Teddy3MonV1Req.class, Teddy3MonV1Resp::parse, Teddy3MonV1Resp.class, true),
    MODEL_teddy_6mon_v1("teddy_6mon_v1", ReportTypeEnum.MODEL_teddy_6mon_v1, Teddy6MonV1Req::parse, Teddy6MonV1Req.class, Teddy6MonV1Resp::parse, Teddy6MonV1Resp.class, true),
    MODEL_teddy_12mon_v1("teddy_12mon_v1", ReportTypeEnum.MODEL_teddy_12mon_v1, Teddy12MonV1Req::parse, Teddy12MonV1Req.class, Teddy12MonV1Resp::parse, Teddy12MonV1Resp.class, true),

    MODEL_OverdueIndex("OverdueIndex", ReportTypeEnum.OverdueIndex, null, OverdueIndexReq.class, null, OverdueIndexResp.class, false),
    MODEL_OverdueIndexVariable("OverdueIndexVariable", ReportTypeEnum.OverdueIndexVariable, null, OverdueIndexVariableReq.class, null, OverdueIndexVariableResp.class, false),
    ;

    final String modelCode;
    final ReportTypeEnum reportType;
    final Function<String, Map<FieldName, ?>> paramBuilder;
    final Class<?> paramsClass;
    final Function<Map<FieldName, ?>, Map<String, Object>> respBuilder;
    final Class<?> respClass;
    final boolean pmml;

    private static final Map<ReportTypeEnum, ModelEnum> reportTypeMap = Arrays.stream(ModelEnum.values())
            .collect(Collectors.toMap(ModelEnum::getReportType, Function.identity()));

    private static final Map<ModelEnum, Evaluator> modelEvaluatorMap = Arrays.stream(ModelEnum.values())
            .filter(ModelEnum::isPmml)
            .collect(Collectors.toMap(Function.identity(), modelEnum -> parse(modelEnum.getModelCode())));
    public List<Parameter> getParams() {
        List<Parameter> params = Parameter.findParams(paramsClass, ParamTypeEnum.IN);
        List<Parameter> outParams = Parameter.findParams(respClass, ParamTypeEnum.OUT);
        params.addAll(outParams);
        return params;
    }

    public static ModelEnum valueOf(ReportTypeEnum reportType) {
        ModelEnum apiEnum = reportTypeMap.get(reportType);
        if (apiEnum == null) {
            throw new IllegalArgumentException("Can't support:" + reportType);
        }
        return apiEnum;
    }

    public Evaluator getModelEvaluator() {
        return modelEvaluatorMap.get(this);
    }

    private static Evaluator parse(String modelCode) {
        Resource modelResource = new ClassPathResource("model/" + modelCode + ".pmml");
        try (InputStream modelInputStream = modelResource.getInputStream()) {

            PMML pmml = PMMLUtil.unmarshal(modelInputStream);
            Evaluator evaluator = new ModelEvaluatorBuilder(pmml)
                    .setModelEvaluatorFactory(ModelEvaluatorFactory.newInstance())
                    .build();
            evaluator.verify();

            return evaluator;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
