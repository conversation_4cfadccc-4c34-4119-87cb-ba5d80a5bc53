package com.pintec.tako.infra.facade.guanshu.util;

import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class GuanshuUtil {

    private static final Logger log = LoggerFactory.getLogger(GuanshuUtil.class);
    public static String aesKey;
    public static String clientId;

    public static String string2MD5(String inStr) {
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "";
        }
        char[] charArray = inStr.toCharArray();
        byte[] byteArray = new byte[charArray.length];
        for (int i = 0; i < charArray.length; i++)
            byteArray[i] = (byte) charArray[i];
        byte[] md5Bytes = md5.digest(byteArray);
        StringBuffer hexValue = new StringBuffer();
        for (int i = 0; i < md5Bytes.length; i++) {
            int val = ((int) md5Bytes[i]) & 0xff;
            if (val < 16)
                hexValue.append("0");
            hexValue.append(Integer.toHexString(val));
        }
        return hexValue.toString().toLowerCase();
    }

    public static String aesDecrypt(String encrypted) {
        return aesDecrypt(aesKey, encrypted);
    }
    /**
     * 解密
     *
     * @param keyStr 密钥
     * @param value  解密数据
     * @return
     * @throws NoSuchPaddingException
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     * @throws BadPaddingException
     * @throws IllegalBlockSizeException
     * @throws UnsupportedEncodingException
     */
    public static String aesDecrypt(String keyStr, String encrypted) {
        try {
            String MD5Str = string2MD5(keyStr);// 调整为32 位16 进制
            byte[] keyBytes = Hex.decodeHex(MD5Str.toCharArray());//转换为128 位二进制数据
            Key key = new SecretKeySpec(keyBytes, "AES");// Key 转换
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");// 解密
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] decodeResult =
                    cipher.doFinal(Hex.decodeHex(encrypted.toCharArray()));
            return new String(decodeResult, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static String aesEncrypt(String srcStr) {
        return aesEncrypt(aesKey, srcStr);
    }
    /**
     * 加密
     *
     * @param keyStr 密钥
     * @param srcStr 加密数据
     * @return
     * @throws NoSuchPaddingException
     * @throws InvalidKeyException
     * @throws BadPaddingException
     * @throws IllegalBlockSizeException
     * @throws UnsupportedEncodingException
     */
    public static String aesEncrypt(String keyStr, String srcStr) {
        try {
            String MD5Str = string2MD5(keyStr);// 调整为32 位16 进制
            byte[] keyBytes = Hex.decodeHex(MD5Str.toCharArray());//转换为128 位二进制数据
            Key key = new SecretKeySpec(keyBytes, "AES");// Key 转换
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");// 加密
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] encodeResult =
                    cipher.doFinal(srcStr.getBytes(StandardCharsets.UTF_8));
            return Hex.encodeHexString(encodeResult);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

}