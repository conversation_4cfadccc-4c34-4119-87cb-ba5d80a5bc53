package com.pintec.tako.infra.facade.maisuitx.resp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.constant.Constant;
import com.pintec.tako.common.enums.DataTypeEnum;
import com.pintec.tako.common.enums.ParamTypeEnum;
import com.pintec.tako.common.support.InvokeResponse;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.domain.datasource.entity.Parameter;
import com.pintec.tako.infra.facade.maisuitx.enums.MaisuitxResponseEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
public class PhoneSmsStatV2Resp extends InvokeResponse {

    public static final String OUT_KEY = "maisui_v1_phone_stat_sms_v2";

    @CommentDesc("")
    private String maisui_v1_phone_stat_sms_v2;

    @CommentDesc("")
    private String phone;
    @CommentDesc("")
    private BigDecimal d7_od_num_sum;
    @CommentDesc("")
    private BigDecimal d7_m2a_od_num_sum;
    @CommentDesc("")
    private BigDecimal d7_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal d7_m2a_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal d15_od_num_sum;
    @CommentDesc("")
    private BigDecimal d15_m2a_od_num_sum;
    @CommentDesc("")
    private BigDecimal d15_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal d15_m2a_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal m1_od_num_sum;
    @CommentDesc("")
    private BigDecimal m1_m2a_od_num_sum;
    @CommentDesc("")
    private BigDecimal m1_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal m1_m2a_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal m3_od_num_sum;
    @CommentDesc("")
    private BigDecimal m3_m2a_od_num_sum;
    @CommentDesc("")
    private BigDecimal m3_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal m3_m2a_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal m6_od_num_sum;
    @CommentDesc("")
    private BigDecimal m6_m2a_od_num_sum;
    @CommentDesc("")
    private BigDecimal m6_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal m6_m2a_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal m12_od_num_sum;
    @CommentDesc("")
    private BigDecimal m12_m2a_od_num_sum;
    @CommentDesc("")
    private BigDecimal m12_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal m12_m2a_od_orgcnt_dist;
    @CommentDesc("")
    private BigDecimal d7_ms_od1_cnt;
    @CommentDesc("")
    private BigDecimal d7_ms_od2_cnt;
    @CommentDesc("")
    private BigDecimal d7_ms_od3_cnt;
    @CommentDesc("")
    private BigDecimal d7_ms_od4_cnt;
    @CommentDesc("")
    private BigDecimal d7_ms_od5_cnt;
    @CommentDesc("")
    private BigDecimal d7_ms_od6_cnt;
    @CommentDesc("")
    private BigDecimal d7_ms_od7_cnt;
    @CommentDesc("")
    private BigDecimal d7_ms_od8_cnt;
    @CommentDesc("")
    private BigDecimal d15_ms_od1_cnt;
    @CommentDesc("")
    private BigDecimal d15_ms_od2_cnt;
    @CommentDesc("")
    private BigDecimal d15_ms_od3_cnt;
    @CommentDesc("")
    private BigDecimal d15_ms_od4_cnt;
    @CommentDesc("")
    private BigDecimal d15_ms_od5_cnt;
    @CommentDesc("")
    private BigDecimal d15_ms_od6_cnt;
    @CommentDesc("")
    private BigDecimal d15_ms_od7_cnt;
    @CommentDesc("")
    private BigDecimal d15_ms_od8_cnt;
    @CommentDesc("")
    private BigDecimal m1_ms_od1_cnt;
    @CommentDesc("")
    private BigDecimal m1_ms_od2_cnt;
    @CommentDesc("")
    private BigDecimal m1_ms_od3_cnt;
    @CommentDesc("")
    private BigDecimal m1_ms_od4_cnt;
    @CommentDesc("")
    private BigDecimal m1_ms_od5_cnt;
    @CommentDesc("")
    private BigDecimal m1_ms_od6_cnt;
    @CommentDesc("")
    private BigDecimal m1_ms_od7_cnt;
    @CommentDesc("")
    private BigDecimal m1_ms_od8_cnt;
    @CommentDesc("")
    private BigDecimal m3_ms_od1_cnt;
    @CommentDesc("")
    private BigDecimal m3_ms_od2_cnt;
    @CommentDesc("")
    private BigDecimal m3_ms_od3_cnt;
    @CommentDesc("")
    private BigDecimal m3_ms_od4_cnt;
    @CommentDesc("")
    private BigDecimal m3_ms_od5_cnt;
    @CommentDesc("")
    private BigDecimal m3_ms_od6_cnt;
    @CommentDesc("")
    private BigDecimal m3_ms_od7_cnt;
    @CommentDesc("")
    private BigDecimal m3_ms_od8_cnt;
    @CommentDesc("")
    private BigDecimal m6_ms_od1_cnt;
    @CommentDesc("")
    private BigDecimal m6_ms_od2_cnt;
    @CommentDesc("")
    private BigDecimal m6_ms_od3_cnt;
    @CommentDesc("")
    private BigDecimal m6_ms_od4_cnt;
    @CommentDesc("")
    private BigDecimal m6_ms_od5_cnt;
    @CommentDesc("")
    private BigDecimal m6_ms_od6_cnt;
    @CommentDesc("")
    private BigDecimal m6_ms_od7_cnt;
    @CommentDesc("")
    private BigDecimal m6_ms_od8_cnt;
    @CommentDesc("")
    private BigDecimal m12_ms_od1_cnt;
    @CommentDesc("")
    private BigDecimal m12_ms_od2_cnt;
    @CommentDesc("")
    private BigDecimal m12_ms_od3_cnt;
    @CommentDesc("")
    private BigDecimal m12_ms_od4_cnt;
    @CommentDesc("")
    private BigDecimal m12_ms_od5_cnt;
    @CommentDesc("")
    private BigDecimal m12_ms_od6_cnt;
    @CommentDesc("")
    private BigDecimal m12_ms_od7_cnt;
    @CommentDesc("")
    private BigDecimal m12_ms_od8_cnt;
    @CommentDesc("")
    private String m12_od_date_start;
    @CommentDesc("")
    private String m12_od_date_last;

    @CommentDesc("")
    private BigDecimal his_od_days_last;
    @CommentDesc("")
    private BigDecimal his_od_days_start;

    public static InvokeResponse parse(String json) {
        if (StrUtil.isBlank(json)) {
            json = "{}";
        }

        MaisuitxResp<List<Object>> resp = JsonUtil.parse(json, new TypeReference<MaisuitxResp<List<Object>>>() {});
        List<Parameter> params = Parameter.findParams(PhoneSmsStatV2Resp.class, ParamTypeEnum.OUT);
        for (Parameter param : params) {
            if (param.getType() == DataTypeEnum.BigDecimal) {
                resp.getDataMap().put(param.getName(), Constant.DEFAULT_MODEL_VALUE);
            } else if (param.getType() == DataTypeEnum.String) {
                resp.getDataMap().put(param.getName(), "-8887");
            }
        }
        resp.getDataMap().put(OUT_KEY, "-1");

        if (MaisuitxResponseEnum.isSuccess(resp)) {
            resp.setApiInvokeSuccess(true);
            if (CollUtil.isNotEmpty(resp.getData())) {
                resp.setBillingFlag(true);
                resp.setOutFeatureCount(1);
                String jsonStr = JsonUtil.str(resp.getData().get(0));
                Map<String, Object> map = JsonUtil.parse(jsonStr, new TypeReference<Map<String, Object>>() {});
                resp.getDataMap().putAll(map);
                resp.getDataMap().put(OUT_KEY, jsonStr);
                try {
                    // 计算passedDay
                    DateTime m12OdDateLast = DateUtil.parseDate((String) resp.getDataMap().get("m12_od_date_last"));
                    int passedDay = (int) DateUtil.betweenDay(DateUtil.date(), m12OdDateLast, true);
                    resp.getDataMap().put("his_od_days_last", passedDay);

                    DateTime m12OdDateStart = DateUtil.parseDate((String) resp.getDataMap().get("m12_od_date_start"));
                    int passedStartDay = (int) DateUtil.betweenDay(DateUtil.date(), m12OdDateStart, true);
                    resp.getDataMap().put("his_od_days_start", passedStartDay);

                    resp.getDataMap().put("his_od_date_last", DateUtil.formatDate(m12OdDateLast));
                    resp.getDataMap().put("his_od_date_start", DateUtil.formatDate(m12OdDateStart));
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }

        return resp;
    }
}