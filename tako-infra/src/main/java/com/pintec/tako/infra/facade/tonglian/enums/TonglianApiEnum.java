package com.pintec.tako.infra.facade.tonglian.enums;

import com.pintec.tako.common.enums.ParamTypeEnum;
import com.pintec.tako.common.enums.ReportTypeEnum;
import com.pintec.tako.common.support.BaseReportRequest;
import com.pintec.tako.common.support.InvokeResponse;
import com.pintec.tako.domain.datasource.entity.Parameter;
import com.pintec.tako.infra.facade.tonglian.req.XtysDataMxQlhModel01Param;
import com.pintec.tako.infra.facade.tonglian.req.XtysDataMxQlhModel02Param;
import com.pintec.tako.infra.facade.tonglian.resp.XtysDataMxQlhModel01Resp;
import com.pintec.tako.infra.facade.tonglian.resp.XtysDataMxQlhModel02Resp;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum TonglianApiEnum {

    xtys_data_mx_qlh_model01("/ys-modeling-api/v2/trans/priv_modeling", "/xtys_data_mx_qlh_model01", ReportTypeEnum.tonglian_xtys_data_mx_qlh_model01, "xtys-data-mx-qlh-model01", XtysDataMxQlhModel01Param::parse, XtysDataMxQlhModel01Param.class, XtysDataMxQlhModel01Resp::parse, XtysDataMxQlhModel01Resp.class),
    xtys_data_mx_qlh_model02("/ys-modeling-api/v2/trans/priv_modeling", "/xtys_data_mx_qlh_model02", ReportTypeEnum.tonglian_xtys_data_mx_qlh_model02, "xtys-data-mx-qlh-model02", XtysDataMxQlhModel02Param::parse, XtysDataMxQlhModel02Param.class, XtysDataMxQlhModel02Resp::parse, XtysDataMxQlhModel02Resp.class),

    ;
    final String api;
    final String mockApi;
    final ReportTypeEnum reportType;
    final String productCode;
    final Function<String, BaseReportRequest> paramBuilder;
    final Class<?> paramsClass;
    final Function<String, InvokeResponse> respBuilder;
    final Class<? extends InvokeResponse> respClass;

    private static final Map<ReportTypeEnum, TonglianApiEnum> reportTypeMap = Arrays.stream(TonglianApiEnum.values())
            .collect(Collectors.toMap(TonglianApiEnum::getReportType, Function.identity()));

    public List<Parameter> getParams() {
        List<Parameter> params = Parameter.findParams(paramsClass, ParamTypeEnum.IN);
        List<Parameter> outParams = Parameter.findParams(respClass, ParamTypeEnum.OUT);
        params.addAll(outParams);
        return params;
    }

    public static TonglianApiEnum valueOf(ReportTypeEnum reportType) {
        TonglianApiEnum apiEnum = reportTypeMap.get(reportType);
        if (apiEnum == null) {
            throw new IllegalArgumentException("Can't support:" + reportType);
        }
        return apiEnum;
    }

}
