package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongGdcBuyingTicketsCountResp extends LiantongResp {

    @CommentDesc("")
    private String liantong_gdcBuyingTicketsCount;

    public static LiantongGdcBuyingTicketsCountResp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongGdcBuyingTicketsCountResp.class, "liantong_gdcBuyingTicketsCount");
    }
} 