package com.pintec.tako.infra.facade.model.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.constant.Constant;
import lombok.Data;
import org.dmg.pmml.FieldName;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

@Data
public class Teddy12MonV1Resp {

    private final static String KEY = "teddy_12mon_v1";

    @CommentDesc("")
    private BigDecimal teddy_12mon_v1;

    public static Map<String, Object> parse(Map<FieldName, ?> resultMap) {
        Map<String, Object> map = new HashMap<>();
        map.put(KEY, Constant.NEGATIVE_ONE);

        if (resultMap == null) {
            return map;
        }
        Object prob = resultMap.get(FieldName.create("probability(1)"));
        if (prob == null) {
            return map;
        }
        BigDecimal value = new BigDecimal(prob.toString());
        if (value.compareTo(BigDecimal.ZERO) >= 0 && value.compareTo(BigDecimal.ONE) <= 0) {
            map.put(KEY, value.setScale(6, RoundingMode.HALF_UP));
        }
        return map;
    }
} 