package com.pintec.tako.infra.facade.tonglian;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.pintec.mflow.facade.computeservice.dto.ComputeRequestDTO;
import com.pintec.tako.common.enums.*;
import com.pintec.tako.common.support.BaseReportRequest;
import com.pintec.tako.common.support.InvokeResponse;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.common.util.MetricsUtil;
import com.pintec.tako.domain.datasource.entity.Parameter;
import com.pintec.tako.domain.datasource.facade.DataSourceFacade;
import com.pintec.tako.domain.datasource.service.impl.CommonService;
import com.pintec.tako.domain.intf.entity.Interface;
import com.pintec.tako.domain.intflog.entity.InterfaceLog;
import com.pintec.tako.domain.intflog.service.InterfaceLogService;
import com.pintec.tako.infra.config.support.rest.datasource.DataSourceRestClientBuilder;
import com.pintec.tako.infra.facade.tonglian.config.TonglianProperty;
import com.pintec.tako.infra.facade.tonglian.enums.TonglianApiEnum;
import com.pintec.tako.infra.facade.tonglian.util.TonglianUtil;
import com.pintec.tako.infra.facade.yujian.util.AESUtil;
import com.pintec.tako.infra.facade.yujian.util.SignUtil;
import com.pintec.tako.property.TakoProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Service(value = "tonglianFacade")
public class TonglianFacade implements DataSourceFacade {

    @Autowired
    private CommonService commonService;
    @Resource
    private DataSourceRestClientBuilder restClientBuilder;
    @Autowired
    private InterfaceLogService interfaceLogService;
    @Autowired
    private TonglianProperty tonglianProperty;
    @Autowired
    private TakoProperty takoProperty;

    @Override
    public Map<String, Object> process(ComputeRequestDTO req, ReportTypeEnum reportTypeEnum, Interface anInterface, InterfaceLog interfaceLog) {
        Map<String, Object> inputParam = req.getExtra();
        List<String> outputParams = req.getOutputParams();
        log.info("Tonglian:reportType:{} inputParam:{} outputParams:{}", reportTypeEnum.getName(), inputParam, outputParams);

        // 准备request
        TonglianApiEnum apiEnum = TonglianApiEnum.valueOf(reportTypeEnum);
        BaseReportRequest baseReportRequest = apiEnum.getParamBuilder().apply(JsonUtil.str(inputParam));
        baseReportRequest.setCaller(req.getCaller());
        baseReportRequest.setReqSn(req.getCallerSN());

        // 查缓存
        InvokeResponse invokeResponse = commonService.getFromCache(reportTypeEnum.getCacheKeyEnum(), baseReportRequest);
        if (invokeResponse == null) {
            // 请求数据源
            invokeResponse = request(apiEnum, baseReportRequest);
        }

        // 反序列化响应
        InvokeResponse invokeResp = apiEnum.getRespBuilder().apply(invokeResponse.getResponse());

        // api调用成功
        if (invokeResp.isApiInvokeSuccess()) {
            interfaceLog.setResult(InterfaceResultStatusEnum.SUCCESS.getCode());
            // 数据源调用成功则缓存，是通过缓存查出来的结果就不需要缓存了
            if (invokeResponse.getInvokeRet() == InvokeRetEnum.SUCCESS_INVOKE_DS) {
                commonService.addCache(reportTypeEnum.getCacheKeyEnum(), baseReportRequest, invokeResponse.getResponse());
            }
        }

        // 计费
        if (invokeResp.isBillingFlag()) {
            interfaceLog.setPrice(NumberUtil.mul(anInterface.getPrice(), invokeResp.getOutFeatureCount()));
            if (reportTypeEnum.getBillingMethod() == BillingMethodEnum.feature) {
                interfaceLogService.addInterfaceLogDetail(interfaceLog, invokeResp.getFeatureMap());
            }
        }

        // 更新调用日志
        interfaceLog.setInFeatureCount(baseReportRequest.getInFeatureCount());
        interfaceLog.setOutFeatureCount(invokeResp.getOutFeatureCount());
        interfaceLog.setOutputParam(invokeResponse.getResponse());
        interfaceLog.setCost(invokeResponse.getTimeCost().intValue());
        interfaceLog.setStatus(invokeResponse.getInvokeRet());
        interfaceLogService.update(interfaceLog);

        // 组装响应数据
        Map<String, Object> respMap = new HashMap<>();
        respMap.put(Parameter.In_Feature_Count, baseReportRequest.getInFeatureCount());
        respMap.put(Parameter.Out_Feature_Count, invokeResp.getOutFeatureCount());
        respMap.putAll(invokeResp.getDataMap());
        return respMap;
    }

    private InvokeResponse request(TonglianApiEnum apiEnum, BaseReportRequest baseReportRequest) {
        String reqSn = baseReportRequest.getReqSn();

        String paramData = JsonUtil.str(baseReportRequest);
        TreeMap<String, String> reqMap = JsonUtil.parse(paramData, new TypeReference<TreeMap<String, String>>() {
        });
        reqMap.remove("inFeatureCount");

        String timestamp = System.currentTimeMillis() + "";
        reqMap.put("merId", tonglianProperty.getMerId());
        reqMap.put("productCode", apiEnum.getProductCode());
        reqMap.put("timestamp", timestamp);
        reqMap.put("custOrderId", reqSn);

        log.info("reqMap: {}", JSON.toJSONString(reqMap));

        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("x_allintrust_client_type", "api");
        headerMap.put("x_allintrust_timestamp", timestamp);
        headerMap.put("x_allintrust_tenant", tonglianProperty.getTenantId());
        headerMap.put("x_allintrust_app_key", tonglianProperty.getAppKey());
        headerMap.put("x_allintrust_key", tonglianProperty.getSecretKey());
        headerMap.put("x_allinpay_key", tonglianProperty.getMerId());
        headerMap.put("x_allintrust_prod_code", apiEnum.getProductCode());
        headerMap.put("x_allintrust_nonce", IdUtil.fastSimpleUUID());
        headerMap.put("x_allintrust_sign_type", "RSA");
        headerMap.put("encrypt_type", "RSA");
        String signContent = TonglianUtil.buildSignContent(reqMap, true, true, tonglianProperty.getTonglianPublicKey());
        byte[] signByte = SecureUtil.sign(SignAlgorithm.SHA1withRSA, tonglianProperty.getQilehuiPrivateKey(), null).sign(signContent + "&" + timestamp);
        String sign = Base64.encodeBase64String(signByte);
        headerMap.put("x_allintrust_sign", sign);

        log.info("headerMap: {}", JSON.toJSONString(headerMap));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        for (Map.Entry<String, Object> entry : headerMap.entrySet()) {
            headers.set(entry.getKey(), String.valueOf(entry.getValue()));
        }
        HttpEntity<String> requestEntity = new HttpEntity<>(signContent, headers);
        String url = getApiUrl(apiEnum);

        InvokeResponse response = new InvokeResponse();
        long start = System.currentTimeMillis();
        try {
            log.info("[tonglian] reqSn: {}, url: {}, reqMap: {}", reqSn, url, JSON.toJSONString(reqMap));
            ResponseEntity<String> exchange = restClientBuilder.build(DataSourceEnum.Tonglian).exchange(url, HttpMethod.POST, requestEntity, String.class);
            log.info("[tonglian] reqSn: {}, response: {}", reqSn, exchange);
            response.setInvokeRet(InvokeRetEnum.SUCCESS_INVOKE_DS);
            response.setResponse(exchange.getBody());
        } catch (Throwable e) {
            MetricsUtil.recordDsException(DataSourceEnum.Tonglian, apiEnum.getReportType(), e.getMessage());
            response.setInvokeRet(InvokeRetEnum.EXCEPTION_INVOKE_DS);
            log.error("[tonglian] reqSn: {}", reqSn, e);
        } finally {
            long cost = System.currentTimeMillis() - start;
            response.setTimeCost(cost);
            log.info("[tonglian] reqSn: {}, cost: {}ms response: {}", reqSn, cost, response);
            MetricsUtil.recordDsCostTime(DataSourceEnum.Tonglian, apiEnum.getReportType(), cost);
        }
        return response;
    }

    private String getApiUrl(TonglianApiEnum apiEnum) {
        if (takoProperty.getEnv().isDev() && tonglianProperty.isMock()) {
            return takoProperty.getMockUrl().concat(apiEnum.getMockApi());
        }
        return tonglianProperty.getApiUrl() + apiEnum.getApi();
    }

    @Override
    public List<Parameter> getParameters(ReportTypeEnum reportTypeEnum) {
        TonglianApiEnum tonglianApiEnum = TonglianApiEnum.valueOf(reportTypeEnum);
        return tonglianApiEnum.getParams();
    }
}
