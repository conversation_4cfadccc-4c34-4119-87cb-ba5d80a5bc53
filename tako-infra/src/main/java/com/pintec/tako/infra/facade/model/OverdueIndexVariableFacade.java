package com.pintec.tako.infra.facade.model;

import com.pintec.mflow.facade.computeservice.dto.ComputeRequestDTO;
import com.pintec.tako.common.enums.ReportTypeEnum;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.domain.datasource.entity.Parameter;
import com.pintec.tako.domain.datasource.facade.DataSourceFacade;
import com.pintec.tako.domain.intf.entity.Interface;
import com.pintec.tako.domain.intflog.entity.InterfaceLog;
import com.pintec.tako.infra.facade.model.enums.ModelEnum;
import com.pintec.tako.infra.facade.model.resp.OverdueIndexVariableResp;
import lombok.extern.slf4j.Slf4j;

import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.io.IOException;

import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service("overdueIndexVariableFacade")
public class OverdueIndexVariableFacade implements DataSourceFacade {

    private static final String CSV_FILE_PATH = "OverdueIndexVariable/df_qy_basic_group300.csv";
    private static final Random RANDOM = new Random();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    // 使用TreeMap支持二分查找，按calculated_yy_rounded分组存储CSV数据
    private final NavigableMap<BigDecimal, List<OverdueIndexVariableResp>> dataMap = new TreeMap<>();

    /**
     * 启动时加载CSV数据
     */
    @PostConstruct
    public void loadCsvData() {
        log.info("开始加载CSV数据文件: {}", CSV_FILE_PATH);
        try {
            ClassPathResource resource = new ClassPathResource(CSV_FILE_PATH);
            Path tempFile = Files.createTempFile("csv_data", ".csv");

            // 将classpath资源复制到临时文件
            try (InputStream inputStream = resource.getInputStream()) {
                Files.copy(inputStream, tempFile, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
            }

            // 使用Files.lines读取CSV数据
            try (Stream<String> lines = Files.lines(tempFile)) {
                List<OverdueIndexVariableResp> records = lines
                        .skip(1) // 跳过表头
                        .map(line -> line.split(","))
                        .map(this::parseArrayToRecord)
                        .filter(Objects::nonNull)
                        .filter(record -> record.getCalculated_yy_rounded() != null)
                        .collect(Collectors.toList());

                // 按calculated_yy_rounded分组
                dataMap.putAll(records.stream()
                        .collect(Collectors.groupingBy(
                                OverdueIndexVariableResp::getCalculated_yy_rounded,
                                TreeMap::new,
                                Collectors.toList()
                        )));

                log.info("CSV数据加载完成，总记录数: {}, 有效记录数: {}, 分组数: {}",
                        records.size() + 1, records.size(), dataMap.size()); // +1 for header

                // 打印分组统计信息
                dataMap.forEach((key, value) ->
                    log.debug("calculated_yy_rounded: {}, 记录数: {}", key, value.size()));

            } finally {
                // 清理临时文件
                Files.deleteIfExists(tempFile);
            }

        } catch (IOException e) {
            log.error("读取CSV文件失败: {}", CSV_FILE_PATH, e);
            throw new RuntimeException("读取CSV文件失败: " + CSV_FILE_PATH, e);
        }
    }

    /**
     * 将CSV行数组解析为OverdueIndexVariableResp对象
     */
    private OverdueIndexVariableResp parseArrayToRecord(String[] arr) {
        try {
            if (arr.length < 19) { // 至少需要19个字段
                log.warn("CSV行字段数不足: {}", arr.length);
                return null;
            }

            OverdueIndexVariableResp record = new OverdueIndexVariableResp();
            record.setCalculated_yy_rounded(new BigDecimal(arr[0].trim()));
            record.setM1_od_num_sum(parseIntValue(arr[1]));
            record.setM1_m2a_od_num_sum(parseIntValue(arr[2]));
            record.setM1_od_orgcnt_dist(parseIntValue(arr[3]));
            record.setM1_m2a_od_orgcnt_dist(parseIntValue(arr[4]));
            record.setM3_od_num_sum(parseIntValue(arr[5]));
            record.setM3_m2a_od_num_sum(parseIntValue(arr[6]));
            record.setM3_od_orgcnt_dist(parseIntValue(arr[7]));
            record.setM3_m2a_od_orgcnt_dist(parseIntValue(arr[8]));
            record.setM6_od_num_sum(parseIntValue(arr[9]));
            record.setM6_m2a_od_num_sum(parseIntValue(arr[10]));
            record.setM6_od_orgcnt_dist(parseIntValue(arr[11]));
            record.setM6_m2a_od_orgcnt_dist(parseIntValue(arr[12]));
            record.setM12_od_num_sum(parseIntValue(arr[13]));
            record.setM12_m2a_od_num_sum(parseIntValue(arr[14]));
            record.setM12_od_orgcnt_dist(parseIntValue(arr[15]));
            record.setM12_m2a_od_orgcnt_dist(parseIntValue(arr[16]));
            record.setHis_od_days_start(parseIntValue(arr[17]));
            record.setHis_od_days_last(parseIntValue(arr[18]));

            return record;
        } catch (Exception e) {
            log.warn("解析CSV行失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析整数值，处理可能的小数点
     */
    private Integer parseIntValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return 0;
        }
        try {
            double doubleValue = Double.parseDouble(value.trim());
            return (int) doubleValue;
        } catch (NumberFormatException e) {
            log.warn("解析整数失败: {}", value);
            return 0;
        }
    }

    /**
     * 找到与输入yy值最接近的calculated_yy_rounded
     * 使用TreeMap的floorKey/ceilingKey进行二分查找
     */
    private BigDecimal findClosestValue(BigDecimal yy) {
        if (dataMap.isEmpty()) {
            throw new RuntimeException("CSV数据未加载或为空");
        }

        BigDecimal floor = dataMap.floorKey(yy);
        BigDecimal ceiling = dataMap.ceilingKey(yy);

        // 找最接近的key
        if (floor == null) {
            return ceiling;
        } else if (ceiling == null) {
            return floor;
        } else {
            // 比较绝对值差值，选择更接近的
            BigDecimal floorDiff = yy.subtract(floor).abs();
            BigDecimal ceilingDiff = ceiling.subtract(yy).abs();
            return floorDiff.compareTo(ceilingDiff) <= 0 ? floor : ceiling;
        }
    }

    /**
     * 从相同calculated_yy_rounded的记录中随机选择一条
     */
    private OverdueIndexVariableResp randomSelectRecord(BigDecimal closestValue) {
        List<OverdueIndexVariableResp> records = dataMap.get(closestValue);
        if (records == null || records.isEmpty()) {
            throw new RuntimeException("未找到对应的记录: " + closestValue);
        }
        return records.get(RANDOM.nextInt(records.size()));
    }

    /**
     * 计算日期字段并完善响应对象
     */
    private OverdueIndexVariableResp convertToResponse(OverdueIndexVariableResp record) {
        if (record == null) {
            return null;
        }

        // 计算日期字段
        LocalDate currentDate = LocalDate.now();

        if (record.getHis_od_days_start() != null) {
            LocalDate startDate = currentDate.minusDays(record.getHis_od_days_start());
            record.setM12_od_date_start(startDate.format(DATE_FORMATTER));
        }

        if (record.getHis_od_days_last() != null) {
            LocalDate lastDate = currentDate.minusDays(record.getHis_od_days_last());
            record.setM12_od_date_last(lastDate.format(DATE_FORMATTER));
        }

        return record;
    }

    @Override
    public Map<String, Object> process(ComputeRequestDTO req, ReportTypeEnum reportTypeEnum, Interface anInterface, InterfaceLog interfaceLog) {
        Map<String, Object> inputParam = req.getExtra();
        List<String> outputParams = req.getOutputParams();
        log.info("Model:reportType:{} inputParam:{} outputParams:{}", reportTypeEnum.getName(), inputParam, outputParams);

        Object yyVal = inputParam.get("yy") ;
        if (yyVal == null) {
            log.error("输入参数yy不能为空");
            throw new RuntimeException("输入参数yy不能为空");
        }
        BigDecimal yy = new BigDecimal(yyVal.toString());

        log.info("开始处理逾期指标变量查询，输入yy值: {}", yy);

        try {
            // 1. 查找最接近的calculated_yy_rounded值
            BigDecimal closestValue = findClosestValue(yy);
            log.info("找到最接近的calculated_yy_rounded值: {}, 输入yy: {}", closestValue, yy);

            // 2. 从相同calculated_yy_rounded的记录中随机选择一条
            OverdueIndexVariableResp selectedRecord = randomSelectRecord(closestValue);
            log.info("随机选择的记录: calculated_yy_rounded={}, his_od_days_start={}, his_od_days_last={}",
                    selectedRecord.getCalculated_yy_rounded(),
                    selectedRecord.getHis_od_days_start(),
                    selectedRecord.getHis_od_days_last());

            // 3. 计算日期字段并完善响应对象
            OverdueIndexVariableResp finalRecord = convertToResponse(selectedRecord);
            log.info("计算日期字段完成: m12_od_date_start={}, m12_od_date_last={}",
                    finalRecord.getM12_od_date_start(),
                    finalRecord.getM12_od_date_last());

            // 4. 转换为结果Map
            Map<String, Object> resultMap = JsonUtil.parse(JsonUtil.str(finalRecord), Map.class);

            log.info("Model:reportType:{} 处理完成，返回记录数: 1", reportTypeEnum.getName());
            return resultMap;

        } catch (Exception e) {
            log.error("处理逾期指标变量查询失败，输入yy: {}, 错误: {}", yy, e.getMessage(), e);
            throw new RuntimeException("处理逾期指标变量查询失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Parameter> getParameters(ReportTypeEnum reportTypeEnum) {
        ModelEnum apiEnum = ModelEnum.valueOf(reportTypeEnum);
        return apiEnum.getParams();
    }
}
