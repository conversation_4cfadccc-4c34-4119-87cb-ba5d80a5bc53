package com.pintec.tako.infra.facade.model;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.pintec.mflow.facade.computeservice.dto.ComputeRequestDTO;
import com.pintec.tako.common.enums.ReportTypeEnum;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.domain.datasource.entity.Parameter;
import com.pintec.tako.domain.datasource.facade.DataSourceFacade;
import com.pintec.tako.domain.intf.entity.Interface;
import com.pintec.tako.domain.intflog.entity.InterfaceLog;
import com.pintec.tako.infra.facade.model.enums.ModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service("overdueIndexVariableFacade")
public class OverdueIndexVariableFacade implements DataSourceFacade {

    @Override
    public Map<String, Object> process(ComputeRequestDTO req, ReportTypeEnum reportTypeEnum, Interface anInterface, InterfaceLog interfaceLog) {
        Map<String, Object> inputParam = req.getExtra();
        List<String> outputParams = req.getOutputParams();
        log.info("Model:reportType:{} inputParam:{} outputParams:{}", reportTypeEnum.getName(), inputParam, outputParams);

        BigDecimal yy = (BigDecimal) inputParam.get("yy");
        Map<String, Object> resultMap = new HashMap<>();

        // TODO 这里有一份csv 位于tako/tako-infra/src/main/resources/OverdueIndexVariable/df_qy_basic_group300.csv 有5000行左右，要在此处实现一个逻辑：
        // 需求是从外部传入一个值yy，在csv中找出与yy最接近的calculated_yy_rounded，然后从等于calculated_yy_rounded值的记录中随机取一条, 然后结合OverdueIndexVariableResp定义的字段和逻辑，用最优雅的方式在此处实现。
        // csv类似如下结构：
        // calculated_yy_rounded,m1_od_num_sum,m1_m2a_od_num_sum,m1_od_orgcnt_dist,m1_m2a_od_orgcnt_dist,m3_od_num_sum,m3_m2a_od_num_sum,m3_od_orgcnt_dist,m3_m2a_od_orgcnt_dist,m6_od_num_sum,m6_m2a_od_num_sum,m6_od_orgcnt_dist,m6_m2a_od_orgcnt_dist,m12_od_num_sum,m12_m2a_od_num_sum,m12_od_orgcnt_dist,m12_m2a_od_orgcnt_dist,days_diff_start,days_diff_last
        // 7.825,0,0,0,0,0,0,0,0,13,8,6,3,14,8,6,3,183,95
        // 7.825,0,0,0,0,0,0,0,0,1,0,1,0,1,0,1,0,122,122
        // 8.04,1,0,1,0,1,0,1,0,1,0,1,0,4,0,2,0,223,4
        // 8.04,0,0,0,0,1,0,1,0,1,0,1,0,4,3,2,1,297,66
        // 8.04,0,0,0,0,1,0,1,0,1,0,1,0,1,0,1,0,82,82
        // 8.04,0,0,0,0,1,0,1,0,3,0,1,0,4,0,2,0,216,84
        // 8.04,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,13,13
        // 8.255,2,0,1,0,2,0,1,0,2,0,1,0,2,0,1,0,3,2
        // 8.255,0,0,0,0,2,0,1,0,2,0,1,0,2,0,1,0,74,74
        // 8.255,0,0,0,0,2,2,1,1,2,2,1,1,5,2,2,1,341,58
        // 8.255,2,0,1,0,2,0,1,0,2,0,1,0,13,6,6,1,332,1
        // 8.255,0,0,0,0,2,2,1,1,7,6,3,2,7,6,3,2,172,59
        // 8.255,0,0,0,0,2,0,1,0,2,0,1,0,2,0,1,0,59,38
        // ......

        log.info("Model:reportType:{} resultMap:{}", reportTypeEnum.getName(), resultMap);
        return resultMap;
    }

    @Override
    public List<Parameter> getParameters(ReportTypeEnum reportTypeEnum) {
        ModelEnum apiEnum = ModelEnum.valueOf(reportTypeEnum);
        return apiEnum.getParams();
    }
}
