package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongConsumptionAmountAverageResp extends LiantongResp {

    @CommentDesc("")
    private String liantong_consumptionAmountAverage;

    public static LiantongConsumptionAmountAverageResp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongConsumptionAmountAverageResp.class, "liantong_consumptionAmountAverage");
    }
} 