package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongBuyTicketsInAdvanceProportion48Resp extends LiantongResp {

    @CommentDesc("")
    private String liantong_buyTicketsInAdvanceProportion48;

    public static LiantongBuyTicketsInAdvanceProportion48Resp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongBuyTicketsInAdvanceProportion48Resp.class, "liantong_buyTicketsInAdvanceProportion48");
    }
} 