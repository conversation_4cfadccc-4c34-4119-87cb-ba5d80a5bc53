package com.pintec.tako.infra.facade.taidixiong.req;

import com.pintec.tako.infra.facade.taidixiong.config.TaidixiongProperty;
import com.pintec.tako.infra.facade.taidixiong.enums.TaidixiongApiEnum;
import lombok.Data;


@Data
public class TaidixiongHuitongSubscore19Param extends TaidixiongMobileParam {
	public static TaidixiongHuitongSubscore19Param parse(String paramJson) {
		return TaidixiongMobileParam.parse(param<PERSON>son, TaidixiongHuitongSubscore19Param.class);
	}

	@Override
	protected String getProductKey(TaidixiongProperty taidixiongProperty) {
		return "T0201019";
	}

	@Override
	protected TaidixiongApiEnum getApiEnum() {
		return TaidixiongApiEnum.Taidixiong_huitong_subscore19;
	}
}
