package com.pintec.tako.infra.facade.guanshu;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.pintec.mflow.facade.computeservice.dto.ComputeRequestDTO;
import com.pintec.tako.common.enums.*;
import com.pintec.tako.common.support.BaseReportRequest;
import com.pintec.tako.common.support.InvokeResponse;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.common.util.MetricsUtil;
import com.pintec.tako.domain.datasource.entity.Parameter;
import com.pintec.tako.domain.datasource.facade.DataSourceFacade;
import com.pintec.tako.domain.datasource.service.impl.CommonService;
import com.pintec.tako.domain.intf.entity.Interface;
import com.pintec.tako.domain.intflog.entity.InterfaceLog;
import com.pintec.tako.domain.intflog.service.InterfaceLogService;
import com.pintec.tako.infra.config.support.rest.datasource.DataSourceRestClientBuilder;
import com.pintec.tako.infra.facade.guanshu.config.GuanshuProperty;
import com.pintec.tako.infra.facade.guanshu.enums.GuanshuApiEnum;
import com.pintec.tako.property.TakoProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service(value = "guanshuFacade")
public class GuanshuFacade implements DataSourceFacade  {

    @Autowired
    private GuanshuProperty guanshuProperty;
    @Resource
    private DataSourceRestClientBuilder restClientBuilder;
    @Autowired
    private CommonService commonService;
    @Autowired
    private InterfaceLogService interfaceLogService;
    @Autowired
    private TakoProperty takoProperty;

    @Override
    public Map<String, Object> process(ComputeRequestDTO req, ReportTypeEnum reportTypeEnum, Interface anInterface, InterfaceLog interfaceLog) {
        Map<String, Object> inputParam = req.getExtra();
        List<String> outputParams = req.getOutputParams();
        log.info("Guanshu:reportType:{} inputParam:{} outputParams:{}", reportTypeEnum.getName(), inputParam, outputParams);

        GuanshuApiEnum guanshuApiEnum = GuanshuApiEnum.valueOf(reportTypeEnum);
        BaseReportRequest baseReportRequest = guanshuApiEnum.getParamBuilder().apply(JsonUtil.str(inputParam));

        // 查缓存
        InvokeResponse invokeResponse = commonService.getFromCache(reportTypeEnum.getCacheKeyEnum(), baseReportRequest);
        if (invokeResponse == null) {
            // 请求数据源
            invokeResponse = request(req.getCallerSN(), guanshuApiEnum, baseReportRequest);
        }

        // 反序列化响应
        InvokeResponse invokeResp = guanshuApiEnum.getRespBuilder().apply(invokeResponse.getResponse());

        // api调用成功
        if (invokeResp.isApiInvokeSuccess()) {
            interfaceLog.setResult(InterfaceResultStatusEnum.SUCCESS.getCode());
            // 数据源调用成功则缓存，是通过缓存查出来的结果就不需要缓存了
            if (invokeResponse.getInvokeRet() == InvokeRetEnum.SUCCESS_INVOKE_DS) {
                commonService.addCache(reportTypeEnum.getCacheKeyEnum(), baseReportRequest, invokeResponse.getResponse());
            }
        }

        // 计费
        if (invokeResp.isBillingFlag()) {
            interfaceLog.setPrice(NumberUtil.mul(anInterface.getPrice(), invokeResp.getOutFeatureCount()));
            if (reportTypeEnum.getBillingMethod() == BillingMethodEnum.feature) {
                interfaceLogService.addInterfaceLogDetail(interfaceLog, invokeResp.getFeatureMap());
            }
        }

        // 更新调用日志
        interfaceLog.setInFeatureCount(baseReportRequest.getInFeatureCount());
        interfaceLog.setOutFeatureCount(invokeResp.getOutFeatureCount());
        interfaceLog.setOutputParam(invokeResponse.getResponse());
        interfaceLog.setCost(invokeResponse.getTimeCost().intValue());
        interfaceLog.setStatus(invokeResponse.getInvokeRet());
        interfaceLogService.update(interfaceLog);

        // 组装响应数据
        Map<String, Object> respMap = new HashMap<>();
        respMap.put(Parameter.In_Feature_Count, baseReportRequest.getInFeatureCount());
        respMap.put(Parameter.Out_Feature_Count, invokeResp.getOutFeatureCount());
        respMap.putAll(invokeResp.getDataMap());
        return respMap;
    }

    private InvokeResponse request(String reqSn, GuanshuApiEnum apiEnum, BaseReportRequest baseReportRequest) {

        String url = getApiUrl(apiEnum);
        String reqJson = JSON.toJSONString(baseReportRequest);
        Map<String, String> paramMap = JsonUtil.parse(reqJson, new TypeReference<Map<String, String>>(){});
        paramMap.remove("inFeatureCount");

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        paramMap.forEach(params::add);

        InvokeResponse response = new InvokeResponse();
        long start = System.currentTimeMillis();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.add("charset", "UTF-8");

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(params, headers);

            log.info("[guanshu] post sn:{}, url:{}, entity:{}", reqSn, url, entity);
            ResponseEntity<String> exchange = restClientBuilder.build(DataSourceEnum.Guanshu).exchange(url, HttpMethod.POST, entity, String.class);
            log.info("[guanshu] post sn:{},exchange={},param={}", reqSn, exchange, reqJson);

            response.setInvokeRet(InvokeRetEnum.SUCCESS_INVOKE_DS);
            String body = exchange.getBody();
            response.setResponse(body);
            log.info("[guanshu] post sn={},response={}", reqSn, response);
        } catch (Throwable e) {
            MetricsUtil.recordDsException(DataSourceEnum.Guanshu, apiEnum.getReportType(), e.getMessage());
            response.setInvokeRet(InvokeRetEnum.EXCEPTION_INVOKE_DS);
            log.error("[guanshu] post error sn={}, url={}, param={}", reqSn, url, JsonUtil.str(reqJson), e);
        } finally {
            long cost = System.currentTimeMillis() - start;
            log.info("[guanshu] post cost={}ms, sn={},response={}", cost, reqSn, response);
            response.setTimeCost(cost);
            MetricsUtil.recordDsCostTime(DataSourceEnum.Guanshu, apiEnum.getReportType(), cost);
        }

        return response;
    }


    @Override
    public List<Parameter> getParameters(ReportTypeEnum reportTypeEnum) {
        GuanshuApiEnum guanshuApiEnum = GuanshuApiEnum.valueOf(reportTypeEnum);
        return guanshuApiEnum.getParams();
    }

    private String getApiUrl(GuanshuApiEnum apiEnum) {
        if (takoProperty.getEnv().isDev() && guanshuProperty.isMock()) {
            return takoProperty.getMockUrl().concat(apiEnum.getMockApi());
        }
        return guanshuProperty.getApiUrl() + apiEnum.getApi();
    }
}
