package com.pintec.tako.infra.facade.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.common.util.ValidationUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.dmg.pmml.FieldName;

import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Data
public class Teddy3MonV1Req {

    @NotNull
    @CommentDesc("d30_mediation_center_calling_num_cnt")
    private BigDecimal d30_mediation_center_calling_num_cnt;
    @NotNull
    @CommentDesc("d90_court_called_num_cnt")
    private BigDecimal d90_court_called_num_cnt;

    @Data
    public static class Param {
        @JsonProperty("d30_mediation_center_calling_num_cnt")
        private Double d30_mediation_center_calling_num_cnt;
        @JsonProperty("d90_court_called_num_cnt")
        private Double d90_court_called_num_cnt;
    }

    public static Map<FieldName, ?> parse(String json) {
        Param req = JsonUtil.parse(json, Param.class);
        ValidationUtil.validate(req);

        Map<FieldName, Object> map = new HashMap<>();
        Field[] fields = req.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                map.put(FieldName.create(field.getName()), field.get(req));
            } catch (IllegalAccessException e) {
                log.error("反射访问失败", e);
            }
        }

        return map;
    }
} 