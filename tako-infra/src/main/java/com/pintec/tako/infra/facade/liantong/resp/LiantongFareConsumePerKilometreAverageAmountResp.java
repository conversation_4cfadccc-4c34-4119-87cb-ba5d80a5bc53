package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongFareConsumePerKilometreAverageAmountResp extends LiantongResp {

    @CommentDesc("")
    private String liantong_fareConsumePerKilometreAverageAmount;

    public static LiantongFareConsumePerKilometreAverageAmountResp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongFareConsumePerKilometreAverageAmountResp.class, "liantong_fareConsumePerKilometreAverageAmount");
    }
} 