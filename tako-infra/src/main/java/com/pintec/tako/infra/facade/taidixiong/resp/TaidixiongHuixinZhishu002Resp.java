package com.pintec.tako.infra.facade.taidixiong.resp;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.constant.Constant;
import com.pintec.tako.common.enums.DataTypeEnum;
import com.pintec.tako.common.enums.ParamTypeEnum;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.domain.datasource.entity.Parameter;
import com.pintec.tako.infra.facade.maisuitx.resp.PhoneSmsStatV2Resp;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Data
public class TaidixiongHuixinZhishu002Resp extends TaidixiongHuitongSubscoreResp {
	private static final String OUT_KEY = "taidixiong_huixin_zhishu02";

	@CommentDesc("")
	private String taidixiong_huixin_zhishu02;

	@CommentDesc("")
	private BigDecimal d90_court_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d180_mediation_center_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d360_mediation_center_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d90_court_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d90_law_firm_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d30_law_firm_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d180_mediation_center_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d360_law_firm_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d30_court_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d180_court_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d30_court_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d360_mediation_center_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d360_mediation_center_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d30_mediation_center_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d90_mediation_center_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d30_court_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d90_law_firm_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d90_law_firm_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d30_mediation_center_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d180_law_firm_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d180_court_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d90_court_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d90_mediation_center_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d30_court_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d360_law_firm_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d30_mediation_center_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d180_court_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d90_court_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d180_mediation_center_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d30_law_firm_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d30_law_firm_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d90_mediation_center_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d30_mediation_center_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d360_court_call_num_cnt;
	@CommentDesc("")
	private String d360_call_mediation_center_earliest_day;
	@CommentDesc("")
	private BigDecimal d90_mediation_center_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d360_court_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d360_mediation_center_called_num_cnt;
	@CommentDesc("")
	private String d360_call_mediation_center_latest_day;
	@CommentDesc("")
	private BigDecimal d180_mediation_center_calling_num_cnt;
	@CommentDesc("")
	private BigDecimal d180_law_firm_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d30_law_firm_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d180_court_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d90_law_firm_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d360_law_firm_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d180_law_firm_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d180_law_firm_call_num_cnt;
	@CommentDesc("")
	private BigDecimal d360_court_called_num_cnt;
	@CommentDesc("")
	private BigDecimal d360_court_call_org_num_dist;
	@CommentDesc("")
	private BigDecimal d360_law_firm_calling_num_cnt;

	@CommentDesc("")
	private String d360_call_court_earliest_day;
	@CommentDesc("")
	private String d360_call_court_latest_day;
	@CommentDesc("")
	private String d360_call_law_firm_earliest_day;
	@CommentDesc("")
	private String d360_call_law_firm_latest_day;

	@CommentDesc("d360_call_court_earliest_day、d360_call_law_firm_earliest_day、d360_call_mediation_center_earliest_day，取最早，都不存在则为-8887")
	private String d360_call_earliest_day;
	@CommentDesc("d360_call_court_latest_day、d360_call_law_firm_latest_day、d360_call_mediation_center_latest_day，取最近，都不存在则为-8887")
	private String d360_call_latest_day;

	@CommentDesc("近12个月是否有通话_网络")
	private BigDecimal m12_has_call_circle;


	public static TaidixiongHuixinZhishu002Resp parse(String respData) {
		if (StrUtil.isBlank(respData)) {
			respData = "{}";
		}
		TaidixiongHuixinZhishu002Resp resp = JsonUtil.parse(respData, TaidixiongHuixinZhishu002Resp.class);
		resp.getDataMap().put(OUT_KEY, Parameter.DEFAULT_VAL);
		resp.getDataMap().put("m12_has_call_circle", Parameter.DEFAULT_VAL_BIGDECIMAL);

		if (resp.success()) {
			resp.setApiInvokeSuccess(true);
			Map<String, Object> content = resp.getContent();
			Object m12_has_call_circle = content.get("m12_has_call_circle");
			if (m12_has_call_circle != null) {
				resp.getDataMap().put("m12_has_call_circle", m12_has_call_circle);
			}
			Object T0301002 = content.get("T0301002");
			if (T0301002 != null && !"-1".equals(T0301002)) {
				resp.getDataMap().put(OUT_KEY, T0301002);
				resp.setBillingFlag(true);
				resp.setOutFeatureCount(1);
			}
		}

		List<Parameter> params = Parameter.findParams(TaidixiongHuixinZhishu002Resp.class, ParamTypeEnum.OUT);
		for (Parameter param : params) {
			if (param.getName().equals(OUT_KEY) || "m12_has_call_circle".equals(param.getName())) {
				continue;
			}
			if (param.getType() == DataTypeEnum.BigDecimal) {
				resp.getDataMap().put(param.getName(), Constant.DEFAULT_MODEL_VALUE);
			} else if (param.getType() == DataTypeEnum.String) {
				resp.getDataMap().put(param.getName(), "-8887");
			}
		}

		String taidixiong_huixin_zhishu02 = (String)resp.getDataMap().get(OUT_KEY);
		if (taidixiong_huixin_zhishu02 == null || "-1".equals(taidixiong_huixin_zhishu02)) {
			return resp;
		}

		Map<String, Object> map = JsonUtil.parse(taidixiong_huixin_zhishu02, new TypeReference<Map<String, Object>>() {});
		process(map);
		map.forEach((k, v) -> {
			if (v != null && !Constant.NEGATIVE_ONE.equals(v)) {
				resp.getDataMap().put(k, v);
			}
		});

		return resp;
	}

	private static void process(Map<String, Object> map) {

		String d360_call_court_earliest_day = map.getOrDefault("d360_call_court_earliest_day", "").toString();
		String d360_call_law_firm_earliest_day = map.getOrDefault("d360_call_law_firm_earliest_day", "").toString();
		String d360_call_mediation_center_earliest_day = map.getOrDefault("d360_call_mediation_center_earliest_day", "").toString();

		String d360_call_court_latest_day = map.getOrDefault("d360_call_court_latest_day", "").toString();
		String d360_call_law_firm_latest_day = map.getOrDefault("d360_call_law_firm_latest_day", "").toString();
		String d360_call_mediation_center_latest_day = map.getOrDefault("d360_call_mediation_center_latest_day", "").toString();

		Date d360_call_court_earliest_day_date = DateUtil.parse(d360_call_court_earliest_day);
		Date d360_call_law_firm_earliest_day_date = DateUtil.parse(d360_call_law_firm_earliest_day);
		Date d360_call_mediation_center_earliest_day_date = DateUtil.parse(d360_call_mediation_center_earliest_day);

		// 计算最早日期
		List<Date> earliestDates = Lists.newArrayList(
				d360_call_court_earliest_day_date,
				d360_call_law_firm_earliest_day_date,
				d360_call_mediation_center_earliest_day_date
		);
		String d360_call_earliest_day = earliestDates.stream()
				.filter(Objects::nonNull)
				.min(Date::compareTo)
				.map(DateUtil::formatDate)
				.orElse(null);


		Date d360_call_court_latest_day_date = DateUtil.parse(d360_call_court_latest_day);
		Date d360_call_law_firm_latest_day_date = DateUtil.parse(d360_call_law_firm_latest_day);
		Date d360_call_mediation_center_latest_day_date = DateUtil.parse(d360_call_mediation_center_latest_day);

		// 计算最晚日期
		List<Date> latestDates = Lists.newArrayList(
				d360_call_court_latest_day_date,
				d360_call_law_firm_latest_day_date,
				d360_call_mediation_center_latest_day_date
		);

		String d360_call_latest_day = latestDates.stream()
				.filter(Objects::nonNull)
				.max(Date::compareTo)
				.map(DateUtil::formatDate)
				.orElse(null);

		map.put("d360_call_earliest_day", d360_call_earliest_day);
		map.put("d360_call_latest_day", d360_call_latest_day);
	}
}
