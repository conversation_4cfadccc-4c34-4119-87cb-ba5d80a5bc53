package com.pintec.tako.infra.facade.youmeng.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import com.pintec.tako.common.support.InvokeResponse;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/4/1 17:00
 */
public class Youmeng_professional_profile_03_accountantResp extends YouMengRespV2 {
    private static final String key = "youmeng_professional_profile_03_accountant";
    protected static final String productId = "1007662";
    @CommentDesc("")
    private BigDecimal youmeng_professional_profile_03_accountant;

    public static InvokeResponse parse(String respData) {
        InvokeResponse resp = doParseV3(respData, key, Youmeng_professional_profile_03_accountantResp.productId);
        return resp;
    }
}
