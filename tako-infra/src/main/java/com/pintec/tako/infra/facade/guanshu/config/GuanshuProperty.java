package com.pintec.tako.infra.facade.guanshu.config;

import com.pintec.tako.infra.facade.guanshu.util.GuanshuUtil;
import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "ds.guanshu")
public class GuanshuProperty implements InitializingBean {
    private boolean mock;
    private String apiUrl;
    private String clientId;
    private String aesKey;

    @Override
    public void afterPropertiesSet() throws Exception {
        GuanshuUtil.aesKey = aesKey;
        GuanshuUtil.clientId = clientId;
    }
}
