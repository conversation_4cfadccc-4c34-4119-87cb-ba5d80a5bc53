package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongRateOfBuyRidingInsuranceCountResp extends LiantongResp {

    @CommentDesc("")
    private String liantong_rateOfBuyRidingInsuranceCount;

    public static LiantongRateOfBuyRidingInsuranceCountResp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongRateOfBuyRidingInsuranceCountResp.class, "liantong_rateOfBuyRidingInsuranceCount");
    }
} 