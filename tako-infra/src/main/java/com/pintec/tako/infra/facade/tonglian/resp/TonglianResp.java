package com.pintec.tako.infra.facade.tonglian.resp;

import com.alibaba.fastjson2.JSON;
import com.pintec.tako.common.support.InvokeResponse;
import com.pintec.tako.infra.facade.tonglian.enums.TonglianResponseEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class TonglianResp extends InvokeResponse {

    /**
     * defaultZone : +8
     * code : 000000
     * msg : 查询成功
     * timestamp : 1748425780512
     * data : {"status": 200, "msg": "success", "score": "0.62656"}
     * seralNo : 17484257805121377343081472593920
     * orderId : 1377343081472593920
     * success : true
     * fee : true
     */

    private String defaultZone;
    private String code;
    private String msg;
    private String timestamp;
    private String data;
    private String seralNo;
    private String orderId;
    private boolean success;
    private boolean fee;

    @lombok.Data
    public static class Data implements Serializable {

        /**
         * status : 200
         * msg : success
         * score : 0.62656
         */
        private int status;
        private String msg;
        private BigDecimal score;
    }


    public static Data data(TonglianResp resp) {
        if (resp == null || resp.getData() == null) {
            return null;
        }
        String data = resp.getData();
        return JSON.parseObject(data, TonglianResp.Data.class);
    }
}
