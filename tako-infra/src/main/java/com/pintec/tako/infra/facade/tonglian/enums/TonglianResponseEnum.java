package com.pintec.tako.infra.facade.tonglian.enums;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.pintec.tako.infra.facade.tonglian.resp.TonglianResp;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum TonglianResponseEnum {

    Code_000000("000000", "查询成功"),
    Code_000001("000001", "查询失败"),
    Code_000010("000010", "未查得相关信息"),
    Code_010100("010100", "系统异常联系管理员"),
    Code_010103("010103", "服务异常"),
    Code_010500("010500", "传入参数错误：格式错误，不符合要求"),
    Code_010501("010501", "身份证格式错误"),
    Code_010502("010502", "手机号格式错误"),
    Code_010503("010503", "姓名格式错误"),
    Code_010504("010504", "银行号格式错误"),
    Code_010505("010505", "商户号格式错误"),
    Code_010506("010506", "商户号有误"),
    Code_012200("012200", "商户异常"),
    Code_012201("012201", "商户不可用"),
    Code_012300("012300", "账户异常"),
    Code_012301("012301", "账户余额不足"),
    Code_012400("012400", "产品异常"),
    Code_012401("012401", "产品不可用"),
    Code_013001("013001", "通道请求连接超时"),
    Code_013002("013002", "通道请求读超时"),
    Code_013011("013011", "通道异常"),
    Code_013021("013021", "渠道账户不可用"),
    Code_013023("013023", "渠道账户余额不足"),
    Code_013025("013025", "渠道返回加解密或加解签失败"),
    Code_GW001("GW001", "验签失败"),
    Code_GW002("GW002", "无服务实例"),
    Code_GW003("GW003", "未设置签名"),
    Code_GW004("GW004", "Post 请求格式错误"),
    Code_GW005("GW005", "白名单验证失败"),
    Code_GW006("GW006", "请求重复提交"),
    Code_GW007("GW007", "请求时间已过期"),
    Code_GW008("GW008", "机构公钥缺少"),
    Code_GW009("GW009", "机构 SecretKey 缺少"),
    Code_GW010("GW010", "参数异常"),
    Code_GW011("GW011", "数据大小限制"),
    Code_GW012("GW012", "服务异常"),
    Code_GW013("GW013", "路由未配置"),
    Code_GW999("GW999", "网关异常"),
    Code_B00001("B00001", "卡已过期"),
    Code_B00003("B00003", "卡未启用"),
    Code_B00005("B00005", "被窃卡"),
    Code_B00007("B00007", "没收卡"),
    Code_B00011("B00011", "卡号状态异常"),
    Code_B00013("B00013", "渠道超时，请稍后重试"),
    Code_B00015("B00015", "预留错误码"),
    Code_B00017("B00017", "该银行暂不支持，请联系客服"),
    Code_B00019("B00019", "暂不支持该银行卡种"),
    Code_B00021("B00021", "该卡片暂不支持或权限受限"),
    Code_B00023("B00023", "银行卡暂不支持该业务，请咨询您"),
    Code_B00025("B00025", "系统问题导致失败，请联系客服"),
    Code_B00027("B00027", "交易失败，详情请咨询您的发卡行"),
    Code_B00029("B00029", "预留错误码"),
    Code_B00031("B00031", "请求参数格式错误"),
    Code_B00033("B00033", "请求信息不全，请补齐"),
    Code_B00035("B00035", "预留错误码"),
    Code_B00037("B00037", "订单重复，请更换商户订单号"),
    Code_B00039("B00039", "参数解密失败"),
    Code_B00041("B00041", "参数校验异常"),
    Code_B00043("B00043", "通道权限受限 , 请联系客服"),
    Code_B00045("B00045", "设置了黑名单，禁止访问"),
    Code_B00047("B00047", "验证次数超限，请次日重试"),
    Code_B00049("B00049", "该交易存在风险，拒绝验证"),
    Code_B00051("B00051", "请求过于频繁，请稍后重试"),
    Code_B00053("B00053", "渠道错误，请联系客服"),
    Code_B00055("B00055", "渠道请求流水号重复，请联系客服"),    ;

    final String code;
    final String desc;

    public static boolean isSuccess(TonglianResp resp) {
        try {
            return Code_000000.getCode().equals(resp.getCode());
        } catch (Exception e) {
            return false;
        }
    }

}
