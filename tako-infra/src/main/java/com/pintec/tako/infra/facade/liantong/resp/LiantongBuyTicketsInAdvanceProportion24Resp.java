package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongBuyTicketsInAdvanceProportion24Resp extends LiantongResp {

    @CommentDesc("")
    private String liantong_buyTicketsInAdvanceProportion24;

    public static LiantongBuyTicketsInAdvanceProportion24Resp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongBuyTicketsInAdvanceProportion24Resp.class, "liantong_buyTicketsInAdvanceProportion24");
    }
} 