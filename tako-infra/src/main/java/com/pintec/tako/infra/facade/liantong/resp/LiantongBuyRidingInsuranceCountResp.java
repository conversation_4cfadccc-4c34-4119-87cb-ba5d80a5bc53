package com.pintec.tako.infra.facade.liantong.resp;

import com.pintec.tako.common.annotation.CommentDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LiantongBuyRidingInsuranceCountResp extends LiantongResp {

    @CommentDesc("")
    private String liantong_buyRidingInsuranceCount;

    public static LiantongBuyRidingInsuranceCountResp parse(String json) {
        return LiantongResp.parseDataResult0_3(json, LiantongBuyRidingInsuranceCountResp.class, "liantong_buyRidingInsuranceCount");
    }
} 