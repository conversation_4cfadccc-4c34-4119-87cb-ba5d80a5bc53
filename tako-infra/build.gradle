
dependencies {
    implementation project(":$APP-common")
    implementation project(":$APP-domain")
    annotationProcessor springBootConfigProcessor
    implementation springBootJdbcStarter
    implementation mysql
    implementation mybatisPlus
    implementation mybatisPlusGenerator
    implementation velocity
    implementation dynamicDatasource
    implementation httpclient
    implementation springWeb
    implementation sequence
    implementation hutoolCrypto
    implementation mflowFacade
    implementation fastJson
    implementation guava
    implementation commonText
    implementation aliyun_dytnsapi20200217

    implementation xstream
    implementation commonsLang
    implementation hystrixCore

    // SDK
    implementation 'com.tencentcloudapi:tencentcloud-sdk-java:3.1.161'
    implementation upaAdvisorsClient

    implementation 'org.bouncycastle:bcpkix-jdk15on:1.70'
    implementation 'org.bouncycastle:bcprov-jdk15on:1.70'
    implementation 'org.bouncycastle:bcprov-jdk15to18:1.76'
    implementation 'cn.pudao:pd-client-sdk:1.2'
    implementation 'com.chinapay:secure-sm:1.0.0:chinapaysecure-sm-1.0'

    implementation 'org.jpmml:pmml-evaluator-metro:1.5.16'
    implementation 'jakarta.xml.bind:jakarta.xml.bind-api:3.0.1'
    implementation 'tech.tablesaw:tablesaw-core:0.43.1'
    implementation 'org.apache.commons:commons-csv:1.14.1'
}