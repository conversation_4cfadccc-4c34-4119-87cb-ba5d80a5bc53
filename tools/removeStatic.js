// This script removes demo app files
const rimraf = require('rimraf');
const fs = require('fs');
const path = require('path');
const packageJson = require('../package.json');

// import rimraf from 'rimraf';
// import fs from 'fs';
// import path  from 'path';
// import packageJson from '../package.json';

const env = process.env.NODE_ENV || "/";

/* eslint-disable no-console */

const pathsToRemove = [
  path.join(__dirname, '../static/' + packageJson.name)
];

function removePath(path, callback) {
  rimraf.sync(path);
  callback();
}

if(env != "production") {
  pathsToRemove.map(path => {
    removePath(path, () => {
      console.log(path + " has been removed.");
    });
  });
}
