package com.pintec.test;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.pintec.tako.TakoApiApplication;
import com.pintec.tako.common.enums.ReportTypeEnum;
import com.pintec.tako.common.util.JsonUtil;
import com.pintec.tako.infra.facade.model.support.OverdueIndexCalculator;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.junit.Assert;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = TakoApiApplication.class)
public class ModelTest extends BaseTest {

    @Test
    public void test_S0301007() {
        Map<String, Object> param = Maps.newHashMap();
        param.put("J0204C556", new BigDecimal("57"));
        param.put("T0201005", new BigDecimal("0.016121531"));
        param.put("M0003S968", new BigDecimal("0.534637"));
        process(ReportTypeEnum.MODEL_S0301007, param);
    }
    @Test
    public void test_T02Y03A04() {
        Map<String, Object> param = Maps.newHashMap();
        param.put("T0202003", new BigDecimal("0.412981"));
        param.put("M0003S968", new BigDecimal("0.293662"));
        param.put("T0202002", new BigDecimal("0.471517"));
        param.put("T0202001", new BigDecimal("0.432327"));
        param.put("Y01ATV04", new BigDecimal("52.3532"));
        // param.put("J01D0005", null);
        process(ReportTypeEnum.MODEL_T02Y03A04, param);
    }
    @Test
    public void test_Y01SUBV2() {
        Map<String, Object> param = Maps.newHashMap();
        param.put("Y01SUBV11", new BigDecimal("42.3104"));
        param.put("Y01SUBV12", new BigDecimal("44.7689"));
        param.put("Y01SUBV13", new BigDecimal("47.6551"));
        param.put("Y01SUBV14", new BigDecimal("57.6249"));
        param.put("Y01SUBV15", new BigDecimal("57.5424"));
        process(ReportTypeEnum.MODEL_Y01SUBV2, param);
    }
    @Test
    public void test_OverdueReminderLevel() {
        Map<String, Object> param = Maps.newHashMap();
        param.put("Y01SUBV11", new BigDecimal("42.3104"));
        param.put("m1_od_num_sum", new BigDecimal("2"));
        param.put("m1_m2a_od_num_sum", new BigDecimal("0"));
        param.put("m6_m2a_od_num_sum", new BigDecimal("0"));
        param.put("r_od_days", new BigDecimal("0"));
        param.put("m12_od_orgcnt_dist", new BigDecimal("2"));
        param.put("m3_od_num_sum", new BigDecimal("4"));
        param.put("m12_od_num_sum", new BigDecimal("4"));
        param.put("m6_od_num_sum", new BigDecimal("4"));
        param.put("m12_m2a_od_num_sum", new BigDecimal("0"));

        process(ReportTypeEnum.MODEL_OverdueReminderLevel, param);
    }
    @Test
    public void test_TSLHPY01S() {
        Map<String, Object> param = Maps.newHashMap();
        param.put("Y01SUBV11", new BigDecimal("42.3104"));
        param.put("Y01SUBV13", new BigDecimal("47.6551"));
        param.put("Y01SUBV12", new BigDecimal("44.7689"));
        param.put("Y01SUBV15", new BigDecimal("57.5424"));
        param.put("Y01SUBV14", new BigDecimal("57.6249"));
        process(ReportTypeEnum.MODEL_TSLHPY01S, param);
    }
    @Test
    public void test_TST10D07() {
        Map<String, Object> param = Maps.newHashMap();
        param.put("T0201010", new BigDecimal("0.440061544"));
        param.put("J01D0007", new BigDecimal("0.0027"));
        process(ReportTypeEnum.MODEL_TST10D07, param);
    }

    @Test
    public void test_st_delinq_score() {
        Map<String, Object> params = new HashMap<>();
        params.put("m1_od_orgcnt_dist", 4);
        params.put("m1_m2a_od_num_sum", 2);
        params.put("m1_od_num_sum", 9);
        // 0.195203
        process(ReportTypeEnum.MODEL_st_delinq_score, params);
    }

    @Test
    public void test_lt_delinq_score() {
        Map<String, Object> params = new HashMap<>();
        params.put("m1_m2a_od_num_sum", 1);
        params.put("m12_od_num_sum", 1);
        params.put("m1_m2a_od_orgcnt_dist", 1);
        params.put("m3_od_num_sum", 1);
        params.put("m1_od_orgcnt_dist", 1);
        params.put("m12_od_orgcnt_dist", 1);
        params.put("m6_od_num_sum", 1);
        params.put("m3_od_orgcnt_dist", 1);
        params.put("m1_od_num_sum", 1);
        // 0.367448
        process(ReportTypeEnum.MODEL_lt_delinq_score, params);
    }

    @Test
    public void test_mt_delinq_score() {
        Map<String, Object> params = new HashMap<>();
        params.put("m1_m2a_od_num_sum", 1);
        params.put("m3_od_num_sum", 1);
        params.put("m1_od_orgcnt_dist", 1);
        params.put("m3_od_orgcnt_dist", 1);
        params.put("m1_od_num_sum", 1);
        // 0.474624
        process(ReportTypeEnum.MODEL_mt_delinq_score, params);
    }

    @Test
    public void test_new_delinq_score() {
        Map<String, Object> params = new HashMap<>();
        params.put("m3_od_num_sum", 1);
        params.put("m3_m2a_od_num_sum", 1);
        params.put("m6_od_orgcnt_dist", 1);
        params.put("m12_m2a_od_num_sum", 1);
        params.put("m12_od_num_sum", 1);
        params.put("m1_od_num_sum", 1);
        // 0.158327
        process(ReportTypeEnum.MODEL_new_delinq_score, params);
    }

    @Test
    public void test_old_delinq_score() {
        Map<String, Object> params = new HashMap<>();
        params.put("m12_od_num_sum", 1);
        params.put("m3_m2a_od_num_sum", 1);
        params.put("m12_m2a_od_num_sum", 1);
        params.put("m6_od_num_sum", 1);
        params.put("m6_m2a_od_num_sum", 1);
        params.put("m3_od_orgcnt_dist", 1);
        // 0.389398
        process(ReportTypeEnum.MODEL_old_delinq_score, params);
    }

    @Test
    void test_overdueIndex0() {
        testOverdueIndex("{}", "{}");
    }

    @Test
    void test_overdueIndex1() {
        testOverdueIndex("{\n" +
                "  \"m1_court_sms_org_num_dist\": 5,\n" +
                " \"m1_mediation_center_sms_org_num_dist\": 0,\n" +
                " \"m3_court_sms_org_num_dist\": 7,\n" +
                " \"m3_mediation_center_sms_org_num_dist\": 0,\n" +
                " \"m6_court_sms_org_num_dist\": 13,\n" +
                " \"m6_mediation_center_sms_org_num_dist\": 1,\n" +
                " \"m1_sus_entrustment_called_call_num_sum\": 0,\n" +
                " \"m1_court_called_call_num_sum\": 0,\n" +
                " \"m1_law_firm_called_call_num_sum\": 0,\n" +
                " \"m1_mediation_center_called_call_num_sum\": 0,\n" +
                " \"m3_sus_entrustment_called_call_num_sum\": 0,\n" +
                " \"m3_court_called_call_num_sum\": 4,\n" +
                " \"m3_law_firm_called_call_num_sum\": 0,\n" +
                " \"m3_mediation_center_called_call_num_sum\": 0,\n" +
                " \"m6_sus_entrustment_called_call_num_sum\": 0,\n" +
                " \"m6_court_called_call_num_sum\": 16,\n" +
                " \"m6_law_firm_called_call_num_sum\": 0,\n" +
                " \"m6_mediation_center_called_call_num_sum\": 0,\n" +
                " \"m1_court_called_call_org_num_dist\": 0,\n" +
                " \"m1_law_firm_called_call_org_num_dist\": 0,\n" +
                " \"m1_mediation_center_called_call_org_num_dist\": 0,\n" +
                " \"m3_court_called_call_org_num_dist\": 1,\n" +
                " \"m3_law_firm_called_call_org_num_dist\": 0,\n" +
                " \"m3_mediation_center_called_call_org_num_dist\": 0,\n" +
                " \"m6_court_called_call_org_num_dist\": 3,\n" +
                " \"m6_law_firm_called_call_org_num_dist\": 0,\n" +
                " \"m6_mediation_center_called_call_org_num_dist\": 0\n" +
                " }", "{}");
    }

    @Test
    void test_overdueIndex2() {
        testOverdueIndex("-1", "{\n" +
                " \"m1_od_num_sum\": 1,\n" +
                " \"m1_m2a_od_num_sum\": 0,\n" +
                " \"m1_od_orgcnt_dist\": 1,\n" +
                " \"m1_m2a_od_orgcnt_dist\": 0,\n" +
                " \"m3_od_num_sum\": 72,\n" +
                " \"m3_m2a_od_num_sum\":0 ,\n" +
                " \"m3_od_orgcnt_dist\": 7,\n" +
                " \"m3_m2a_od_orgcnt_dist\": 2,\n" +
                " \"m6_od_num_sum\": 82,\n" +
                " \"m6_m2a_od_num_sum\":0 ,\n" +
                " \"m6_od_orgcnt_dist\": 12,\n" +
                " \"m6_m2a_od_orgcnt_dist\": 2,\n" +
                " \"m12_od_num_sum\": 82,\n" +
                " \"m12_m2a_od_num_sum\":0 ,\n" +
                " \"m12_od_orgcnt_dist\": 12,\n" +
                " \"m12_m2a_od_orgcnt_dist\": 2\n" +
                " }");
    }

    @Test
    void test_overdueIndex3() {
        testOverdueIndex("{\n" +
                "  \"m1_court_sms_org_num_dist\": 7,\n" +
                "  \"m1_mediation_center_sms_org_num_dist\": 0,\n" +
                "  \"m3_court_sms_org_num_dist\": 11,\n" +
                "  \"m3_mediation_center_sms_org_num_dist\": 0,\n" +
                "  \"m6_court_sms_org_num_dist\": 16,\n" +
                "  \"m6_mediation_center_sms_org_num_dist\": 0,\n" +
                "  \"m1_sus_entrustment_called_call_num_sum\": 0,\n" +
                "  \"m1_court_called_call_num_sum\": 0,\n" +
                "  \"m1_law_firm_called_call_num_sum\": 0,\n" +
                "  \"m1_mediation_center_called_call_num_sum\": 0,\n" +
                "  \"m3_sus_entrustment_called_call_num_sum\": 0,\n" +
                "  \"m3_court_called_call_num_sum\": 0,\n" +
                "  \"m3_law_firm_called_call_num_sum\": 0,\n" +
                "  \"m3_mediation_center_called_call_num_sum\": 0,\n" +
                "  \"m6_sus_entrustment_called_call_num_sum\": 0,\n" +
                "  \"m6_court_called_call_num_sum\": 0,\n" +
                "  \"m6_law_firm_called_call_num_sum\": 0,\n" +
                "  \"m6_mediation_center_called_call_num_sum\": 0,\n" +
                "  \"m1_court_called_call_org_num_dist\": 0,\n" +
                "  \"m1_law_firm_called_call_org_num_dist\": 0,\n" +
                "  \"m1_mediation_center_called_call_org_num_dist\": 0,\n" +
                "  \"m3_court_called_call_org_num_dist\": 0,\n" +
                "  \"m3_law_firm_called_call_org_num_dist\": 0,\n" +
                "  \"m3_mediation_center_called_call_org_num_dist\": 0,\n" +
                "  \"m6_court_called_call_org_num_dist\": 0,\n" +
                "  \"m6_law_firm_called_call_org_num_dist\": 0,\n" +
                "  \"m6_mediation_center_called_call_org_num_dist\": 0}",

                "{\"m1_od_num_sum\": 0,\n" +
                "  \"m1_m2a_od_num_sum\": 0,\n" +
                "  \"m1_od_orgcnt_dist\": 0,\n" +
                "  \"m1_m2a_od_orgcnt_dist\": 0,\n" +
                "  \"m3_od_num_sum\": 0,\n" +
                "  \"m3_m2a_od_num_sum\": 0,\n" +
                "  \"m3_od_orgcnt_dist\": 0,\n" +
                "  \"m3_m2a_od_orgcnt_dist\": 0,\n" +
                "  \"m6_od_num_sum\": 0,\n" +
                "  \"m6_m2a_od_num_sum\": 0,\n" +
                "  \"m6_od_orgcnt_dist\": 0,\n" +
                "  \"m6_m2a_od_orgcnt_dist\": 0,\n" +
                "  \"m12_od_num_sum\": 1,\n" +
                "  \"m12_m2a_od_num_sum\": 0,\n" +
                "  \"m12_od_orgcnt_dist\": 1,\n" +
                "  \"m12_m2a_od_orgcnt_dist\": 0\n" +
                "}");
    }

    private void testOverdueIndex(String taidixiongJson, String meishuiJson) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("taidixiong_huixin_zhishu01", taidixiongJson);
        paramMap.put("maisui_v1_phone_stat_sms", meishuiJson);
        process(ReportTypeEnum.OverdueIndex, paramMap);

    }


    @Test
    void testForOverdueIndexVariable() {
        Map<String, Object> params = new HashMap<>();
        params.put("yy", 8.0);
        process(ReportTypeEnum.OverdueIndexVariable, params);
    }
}
