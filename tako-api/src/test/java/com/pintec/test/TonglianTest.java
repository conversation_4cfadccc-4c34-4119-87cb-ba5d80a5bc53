package com.pintec.test;

import com.google.common.collect.Maps;
import com.pintec.tako.TakoApiApplication;
import com.pintec.tako.common.enums.ReportTypeEnum;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = TakoApiApplication.class,
        value = {"ds.tonglian.mock=false"})
public class TonglianTest extends BaseTest {
    @Test
    public void test_tonglian_xtys_data_mx_qlh_model01() {
        Map<String, Object> param = Maps.newHashMap();
        param.put("cryptoType", "md5");
        // param.put("idCard", DigestUtil.md5Hex("123456789122233344"));
        param.put("mobile", "630c1e755dd066ccb9d1b42b76135408");
        process(ReportTypeEnum.tonglian_xtys_data_mx_qlh_model01, param);
    }
    @Test
    public void test_tonglian_xtys_data_mx_qlh_model02() {
        Map<String, Object> param = Maps.newHashMap();
        param.put("cryptoType", "md5");
        // param.put("idCard", DigestUtil.md5Hex("123456789122233344"));
        param.put("mobile", "630c1e755dd066ccb9d1b42b76135408");
        process(ReportTypeEnum.tonglian_xtys_data_mx_qlh_model02, param);
    }
}
