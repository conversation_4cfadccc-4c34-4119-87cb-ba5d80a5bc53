package com.pintec.test;

import cn.hutool.crypto.SecureUtil;
import com.google.common.collect.Maps;
import com.pintec.tako.TakoApiApplication;
import com.pintec.tako.common.enums.ReportTypeEnum;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = TakoApiApplication.class,
        value = {"ds.guanshu.mock=true"})
public class GuanshuTest extends BaseTest {

    @Test
    public void test_guanshu_getLevelXFZHF01() {
        Map<String, Object> param = Maps.newHashMap();
        param.put("companyName", SecureUtil.md5("国投甘肃售电有限公司"));
        param.put("companyCreditIdentifier", SecureUtil.md5("91620100MA7190544W"));
        param.put("name", SecureUtil.md5("黄基玮1"));
        param.put("idCard", SecureUtil.md5("******************"));
        param.put("phone", SecureUtil.md5("13144291467"));
        process(ReportTypeEnum.guanshu_getLevelXFZHF01, param);
    }

}
